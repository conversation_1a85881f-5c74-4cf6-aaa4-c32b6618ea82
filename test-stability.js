#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Axmol MCP服务器稳定性测试脚本
 */

async function testStability() {
  const results = {
    nodeDirectRun: { success: 0, failed: 0, times: [], avgTime: 0 },
    npmStart: { success: 0, failed: 0, times: [], avgTime: 0 }
  };

  console.log('🧪 开始Axmol MCP服务器稳定性测试...\n');

  // 确保编译后的文件存在
  const distPath = path.join(process.cwd(), 'dist', 'index.js');
  if (!fs.existsSync(distPath)) {
    console.log('❌ 找不到编译后的文件，正在编译...');
    try {
      await runCommand('npm', ['run', 'build']);
      console.log('✅ 编译完成\n');
    } catch (error) {
      console.error('❌ 编译失败:', error.message);
      process.exit(1);
    }
  }

  console.log('📊 测试Node.js直接运行 (10次)...');
  // 测试Node.js直接运行
  for (let i = 0; i < 10; i++) {
    const startTime = Date.now();
    try {
      await runCommandWithTimeout('node', ['dist/index.js', '--help'], 5000);
      const duration = Date.now() - startTime;
      results.nodeDirectRun.success++;
      results.nodeDirectRun.times.push(duration);
      console.log(`  ✅ 测试 ${i + 1}/10 - ${duration}ms`);
    } catch (error) {
      results.nodeDirectRun.failed++;
      console.log(`  ❌ 测试 ${i + 1}/10 失败: ${error.message}`);
    }
  }

  console.log('\n📊 测试npm start运行 (10次)...');
  // 测试npm start运行
  for (let i = 0; i < 10; i++) {
    const startTime = Date.now();
    try {
      await runCommandWithTimeout('npm', ['start', '--', '--help'], 8000);
      const duration = Date.now() - startTime;
      results.npmStart.success++;
      results.npmStart.times.push(duration);
      console.log(`  ✅ 测试 ${i + 1}/10 - ${duration}ms`);
    } catch (error) {
      results.npmStart.failed++;
      console.log(`  ❌ 测试 ${i + 1}/10 失败: ${error.message}`);
    }
  }

  // 计算平均时间
  results.nodeDirectRun.avgTime = results.nodeDirectRun.times.length > 0 
    ? results.nodeDirectRun.times.reduce((a, b) => a + b, 0) / results.nodeDirectRun.times.length 
    : 0;
  
  results.npmStart.avgTime = results.npmStart.times.length > 0 
    ? results.npmStart.times.reduce((a, b) => a + b, 0) / results.npmStart.times.length 
    : 0;

  // 显示结果
  console.log('\n📈 稳定性测试结果:');
  console.log('==========================================');
  
  console.log('\n🔧 Node.js直接运行:');
  console.log(`  ✅ 成功: ${results.nodeDirectRun.success}/10`);
  console.log(`  ❌ 失败: ${results.nodeDirectRun.failed}/10`);
  console.log(`  ⚡ 平均启动时间: ${Math.round(results.nodeDirectRun.avgTime)}ms`);
  console.log(`  📊 成功率: ${(results.nodeDirectRun.success / 10 * 100).toFixed(1)}%`);
  
  console.log('\n📦 npm start运行:');
  console.log(`  ✅ 成功: ${results.npmStart.success}/10`);
  console.log(`  ❌ 失败: ${results.npmStart.failed}/10`);
  console.log(`  ⚡ 平均启动时间: ${Math.round(results.npmStart.avgTime)}ms`);
  console.log(`  📊 成功率: ${(results.npmStart.success / 10 * 100).toFixed(1)}%`);

  // 性能对比
  console.log('\n⚡ 性能对比:');
  if (results.nodeDirectRun.avgTime > 0 && results.npmStart.avgTime > 0) {
    const speedDiff = results.npmStart.avgTime - results.nodeDirectRun.avgTime;
    const speedPercent = ((speedDiff / results.nodeDirectRun.avgTime) * 100).toFixed(1);
    console.log(`  Node.js直接运行比npm start快 ${Math.round(speedDiff)}ms (${speedPercent}%)`);
  }

  // 稳定性评估
  console.log('\n🎯 稳定性评估:');
  const nodeStability = results.nodeDirectRun.success / 10;
  const npmStability = results.npmStart.success / 10;
  
  if (nodeStability >= 0.9) {
    console.log('  🟢 Node.js直接运行: 非常稳定');
  } else if (nodeStability >= 0.7) {
    console.log('  🟡 Node.js直接运行: 基本稳定');
  } else {
    console.log('  🔴 Node.js直接运行: 不稳定');
  }
  
  if (npmStability >= 0.9) {
    console.log('  🟢 npm start运行: 非常稳定');
  } else if (npmStability >= 0.7) {
    console.log('  🟡 npm start运行: 基本稳定');
  } else {
    console.log('  🔴 npm start运行: 不稳定');
  }

  // 推荐
  console.log('\n💡 推荐:');
  if (nodeStability >= npmStability && results.nodeDirectRun.avgTime <= results.npmStart.avgTime) {
    console.log('  🏆 推荐使用 Node.js直接运行 (node dist/index.js)');
    console.log('  📈 理由: 更稳定且启动更快');
  } else {
    console.log('  📦 可以使用 npm start');
  }

  console.log('\n✅ 稳定性测试完成!');
}

/**
 * 运行命令
 */
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`进程退出码: ${code}, stderr: ${stderr}`));
      }
    });
    
    child.on('error', reject);
  });
}

/**
 * 带超时的命令运行
 */
function runCommandWithTimeout(command, args, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe'
    });
    
    const timer = setTimeout(() => {
      child.kill('SIGKILL');
      reject(new Error('命令执行超时'));
    }, timeout);
    
    child.on('close', (code) => {
      clearTimeout(timer);
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`进程退出码: ${code}`));
      }
    });
    
    child.on('error', (error) => {
      clearTimeout(timer);
      reject(error);
    });
  });
}

// 运行测试
testStability().catch(console.error);
