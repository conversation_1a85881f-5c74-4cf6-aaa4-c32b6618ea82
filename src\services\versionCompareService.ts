/**
 * 版本对比服务
 * 负责比较不同 Axmol 版本间的功能差异
 */

import { VersionComparison, VersionChange, CompatibilityInfo, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';

export class VersionCompareService {
  private readonly GITHUB_API_BASE = 'https://api.github.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 4 * 60 * 60 * 1000; // 4小时缓存

  // 版本信息缓存
  private readonly versionCache: Map<string, any> = new Map();
  private readonly changelogCache: Map<string, VersionChange[]> = new Map();

  constructor() {
    this.initializeVersionData();
  }

  /**
   * 比较 Axmol 版本
   */
  async compareAxmolVersions(
    feature: string,
    versions: string[],
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 比较版本: ${versions.join(' vs ')} (功能: ${feature})`);

      // 生成缓存键
      const cacheKey = `version_compare_${feature}_${versions.sort().join('_')}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as VersionComparison | null;
        if (cached) {
          console.log('✅ 从缓存获取版本对比结果');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: 1,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];

      // 1. 获取版本信息
      const versionInfos = await this.getVersionInfos(versions);
      sources.push('github_releases');

      // 2. 分析功能变更
      const changes = await this.analyzeFeatureChanges(feature, versions, versionInfos);
      sources.push('changelog_analysis');

      // 3. 检查兼容性
      const compatibility = await this.checkCompatibility(feature, versions);
      sources.push('compatibility_check');

      // 4. 生成迁移说明
      const migrationNotes = this.generateMigrationNotes(feature, changes);

      const comparison: VersionComparison = {
        feature,
        versions,
        changes,
        compatibility,
        migrationNotes
      };

      // 缓存结果
      if (options.useCache !== false) {
        await defaultCache.set(cacheKey, comparison, this.CACHE_TTL);
      }

      console.log(`✅ 版本对比完成: 找到 ${changes.length} 个变更`);

      return {
        success: true,
        data: comparison,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: changes.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'compareAxmolVersions', { feature, versions, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化版本数据
   */
  private async initializeVersionData(): Promise<void> {
    try {
      // 获取最新的版本信息
      await this.fetchLatestVersions();
      console.log('📊 版本数据初始化完成');
    } catch (error) {
      console.log('⚠️ 版本数据初始化失败:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 获取最新版本列表
   */
  private async fetchLatestVersions(): Promise<void> {
    try {
      const releasesUrl = `${this.GITHUB_API_BASE}/repos/${this.AXMOL_REPO}/releases?per_page=20`;
      const response = await networkUtils.get(releasesUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 10000
      });

      const releases = response.data || [];
      
      for (const release of releases) {
        this.versionCache.set(release.tag_name, {
          version: release.tag_name,
          name: release.name,
          publishedAt: release.published_at,
          body: release.body,
          prerelease: release.prerelease,
          draft: release.draft
        });
      }

      console.log(`📋 缓存了 ${releases.length} 个版本信息`);

    } catch (error) {
      console.log('⚠️ 获取版本列表失败:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 获取版本信息
   */
  private async getVersionInfos(versions: string[]): Promise<Map<string, any>> {
    const versionInfos = new Map<string, any>();

    for (const version of versions) {
      // 先检查缓存
      let versionInfo = this.versionCache.get(version);
      
      if (!versionInfo) {
        // 如果缓存中没有，尝试从 GitHub 获取
        versionInfo = await this.fetchVersionInfo(version);
        if (versionInfo) {
          this.versionCache.set(version, versionInfo);
        }
      }

      if (versionInfo) {
        versionInfos.set(version, versionInfo);
      } else {
        // 如果找不到具体版本信息，创建基础信息
        versionInfos.set(version, {
          version,
          name: version,
          publishedAt: null,
          body: '',
          prerelease: false,
          draft: false
        });
      }
    }

    return versionInfos;
  }

  /**
   * 获取单个版本信息
   */
  private async fetchVersionInfo(version: string): Promise<any | null> {
    try {
      const releaseUrl = `${this.GITHUB_API_BASE}/repos/${this.AXMOL_REPO}/releases/tags/${version}`;
      const response = await networkUtils.get(releaseUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 8000
      });

      return {
        version: response.data.tag_name,
        name: response.data.name,
        publishedAt: response.data.published_at,
        body: response.data.body,
        prerelease: response.data.prerelease,
        draft: response.data.draft
      };

    } catch (error) {
      console.log(`⚠️ 获取版本 ${version} 信息失败:`, error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * 分析功能变更
   */
  private async analyzeFeatureChanges(
    feature: string,
    versions: string[],
    versionInfos: Map<string, any>
  ): Promise<VersionChange[]> {
    const changes: VersionChange[] = [];

    // 按版本时间排序
    const sortedVersions = this.sortVersionsByDate(versions, versionInfos);

    for (const version of sortedVersions) {
      const versionInfo = versionInfos.get(version);
      if (!versionInfo || !versionInfo.body) continue;

      // 分析 changelog 中与功能相关的变更
      const versionChanges = this.parseChangelogForFeature(versionInfo.body, feature, version);
      changes.push(...versionChanges);
    }

    // 如果没有找到具体的变更记录，生成基础对比
    if (changes.length === 0) {
      changes.push(...this.generateBasicChanges(feature, versions));
    }

    return changes;
  }

  /**
   * 按日期排序版本
   */
  private sortVersionsByDate(versions: string[], versionInfos: Map<string, any>): string[] {
    return versions.sort((a, b) => {
      const aInfo = versionInfos.get(a);
      const bInfo = versionInfos.get(b);
      
      if (!aInfo?.publishedAt || !bInfo?.publishedAt) {
        return this.compareVersionStrings(a, b);
      }

      return new Date(aInfo.publishedAt).getTime() - new Date(bInfo.publishedAt).getTime();
    });
  }

  /**
   * 比较版本字符串
   */
  private compareVersionStrings(a: string, b: string): number {
    const aParts = a.replace(/[^\d.]/g, '').split('.').map(Number);
    const bParts = b.replace(/[^\d.]/g, '').split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart !== bPart) {
        return aPart - bPart;
      }
    }
    
    return 0;
  }

  /**
   * 解析 changelog 中的功能变更
   */
  private parseChangelogForFeature(changelog: string, feature: string, version: string): VersionChange[] {
    const changes: VersionChange[] = [];
    const lines = changelog.split('\n');
    
    const featureLower = feature.toLowerCase();
    const featureKeywords = [featureLower, ...this.getFeatureKeywords(feature)];

    for (const line of lines) {
      const lineLower = line.toLowerCase();
      
      // 检查是否包含功能关键词
      if (featureKeywords.some(keyword => lineLower.includes(keyword))) {
        const change = this.parseChangeFromLine(line, version);
        if (change) {
          changes.push(change);
        }
      }
    }

    return changes;
  }

  /**
   * 获取功能相关关键词
   */
  private getFeatureKeywords(feature: string): string[] {
    const keywordMap: { [key: string]: string[] } = {
      'sprite': ['sprite', 'texture', 'image', 'render'],
      'audio': ['audio', 'sound', 'music', 'openal'],
      'physics': ['physics', 'box2d', 'collision', 'body'],
      'ui': ['ui', 'widget', 'button', 'label'],
      'animation': ['animation', 'action', 'tween'],
      'scene': ['scene', 'layer', 'node'],
      'network': ['network', 'http', 'websocket'],
      'file': ['file', 'io', 'storage'],
      'platform': ['platform', 'android', 'ios', 'windows']
    };

    const featureLower = feature.toLowerCase();
    for (const [key, keywords] of Object.entries(keywordMap)) {
      if (featureLower.includes(key) || keywords.some(k => featureLower.includes(k))) {
        return keywords;
      }
    }

    return [];
  }

  /**
   * 从行中解析变更
   */
  private parseChangeFromLine(line: string, version: string): VersionChange | null {
    const trimmedLine = line.trim();
    
    // 检查变更类型
    let changeType: 'added' | 'modified' | 'deprecated' | 'removed' = 'modified';
    let impact: 'breaking' | 'compatible' | 'enhancement' = 'compatible';

    if (trimmedLine.match(/^[+*-]\s*(add|new|introduce)/i)) {
      changeType = 'added';
      impact = 'enhancement';
    } else if (trimmedLine.match(/^[+*-]\s*(remove|delete)/i)) {
      changeType = 'removed';
      impact = 'breaking';
    } else if (trimmedLine.match(/^[+*-]\s*(deprecat|obsolet)/i)) {
      changeType = 'deprecated';
      impact = 'compatible';
    } else if (trimmedLine.match(/^[+*-]\s*(break|chang)/i)) {
      changeType = 'modified';
      impact = 'breaking';
    }

    return {
      version,
      changeType,
      description: trimmedLine.replace(/^[+*-]\s*/, ''),
      impact
    };
  }

  /**
   * 生成基础变更记录
   */
  private generateBasicChanges(feature: string, versions: string[]): VersionChange[] {
    const changes: VersionChange[] = [];
    
    // 为每个版本生成基础变更记录
    versions.forEach((version, index) => {
      if (index === 0) {
        changes.push({
          version,
          changeType: 'added',
          description: `${feature} 功能在版本 ${version} 中可用`,
          impact: 'enhancement'
        });
      } else {
        changes.push({
          version,
          changeType: 'modified',
          description: `${feature} 功能在版本 ${version} 中可能有更新`,
          impact: 'compatible'
        });
      }
    });

    return changes;
  }

  /**
   * 检查兼容性
   */
  private async checkCompatibility(feature: string, versions: string[]): Promise<CompatibilityInfo[]> {
    const compatibility: CompatibilityInfo[] = [];

    for (const version of versions) {
      // 基于版本号和功能类型判断兼容性
      const isCompatible = await this.isFeatureCompatible(feature, version);
      const notes = this.getCompatibilityNotes(feature, version, isCompatible);

      compatibility.push({
        version,
        compatible: isCompatible,
        notes
      });
    }

    return compatibility;
  }

  /**
   * 检查功能是否兼容
   */
  private async isFeatureCompatible(feature: string, version: string): Promise<boolean> {
    // 简化的兼容性检查逻辑
    // 在实际实现中，这里可以查询更详细的兼容性数据库
    
    // 假设较新版本通常向后兼容
    const versionNumber = this.parseVersionNumber(version);
    
    // 基础功能通常在所有版本中都兼容
    const basicFeatures = ['sprite', 'node', 'scene', 'director'];
    if (basicFeatures.includes(feature.toLowerCase())) {
      return true;
    }

    // 较新的功能可能在旧版本中不可用
    const advancedFeatures = ['3d', 'vr', 'ar', 'ml'];
    if (advancedFeatures.includes(feature.toLowerCase()) && versionNumber < 2.0) {
      return false;
    }

    return true;
  }

  /**
   * 解析版本号
   */
  private parseVersionNumber(version: string): number {
    const match = version.match(/(\d+)\.(\d+)/);
    if (match) {
      return parseInt(match[1]) + parseInt(match[2]) * 0.1;
    }
    return 1.0;
  }

  /**
   * 获取兼容性说明
   */
  private getCompatibilityNotes(feature: string, version: string, isCompatible: boolean): string {
    if (isCompatible) {
      return `${feature} 功能在版本 ${version} 中完全兼容`;
    } else {
      return `${feature} 功能在版本 ${version} 中可能不可用或有限制`;
    }
  }

  /**
   * 生成迁移说明
   */
  private generateMigrationNotes(feature: string, changes: VersionChange[]): string[] {
    const notes: string[] = [];

    // 检查是否有破坏性变更
    const breakingChanges = changes.filter(c => c.impact === 'breaking');
    if (breakingChanges.length > 0) {
      notes.push('⚠️ 发现破坏性变更，升级时需要修改代码');
      breakingChanges.forEach(change => {
        notes.push(`- ${change.version}: ${change.description}`);
      });
    }

    // 检查是否有新功能
    const newFeatures = changes.filter(c => c.changeType === 'added');
    if (newFeatures.length > 0) {
      notes.push('✨ 新增功能');
      newFeatures.forEach(change => {
        notes.push(`- ${change.version}: ${change.description}`);
      });
    }

    // 检查是否有弃用功能
    const deprecatedFeatures = changes.filter(c => c.changeType === 'deprecated');
    if (deprecatedFeatures.length > 0) {
      notes.push('🚨 弃用功能');
      deprecatedFeatures.forEach(change => {
        notes.push(`- ${change.version}: ${change.description}`);
      });
    }

    if (notes.length === 0) {
      notes.push(`${feature} 功能在所选版本间保持稳定`);
    }

    return notes;
  }
}

// 导出默认版本对比服务实例
export const versionCompareService = new VersionCompareService();
