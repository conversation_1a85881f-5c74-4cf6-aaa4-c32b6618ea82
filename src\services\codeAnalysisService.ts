/**
 * 代码分析服务
 * 负责分析用户代码并提供 Axmol 最佳实践建议
 */

import { CodeAnalysisResult, CodeIssue, CodeSuggestion, SecurityNote, BestPractice, PerformanceNote, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';

export class CodeAnalysisService {
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟缓存

  // 代码分析规则库
  private readonly analysisRules: Map<string, any> = new Map();
  private readonly bestPracticesDb: Map<string, BestPractice[]> = new Map();

  constructor() {
    this.initializeAnalysisRules();
    this.initializeBestPractices();
  }

  /**
   * 分析 Axmol 代码
   */
  async analyzeAxmolCode(
    code: string,
    language: 'cpp' | 'lua',
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 开始分析 ${language} 代码 (${code.length} 字符)`);

      // 生成缓存键
      const codeHash = this.hashCode(code);
      const cacheKey = `code_analysis_${language}_${codeHash}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as CodeAnalysisResult | null;
        if (cached) {
          console.log('✅ 从缓存获取代码分析结果');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: 1,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = ['static_analysis'];

      // 执行代码分析
      const analysisResult: CodeAnalysisResult = {
        code,
        language,
        issues: [],
        suggestions: [],
        bestPractices: [],
        performance: [],
        security: []
      };

      // 1. 静态代码分析
      analysisResult.issues = this.performStaticAnalysis(code, language);
      
      // 2. 生成改进建议
      analysisResult.suggestions = this.generateSuggestions(code, language, analysisResult.issues);
      
      // 3. 检查最佳实践
      analysisResult.bestPractices = this.checkBestPractices(code, language);
      
      // 4. 性能分析
      analysisResult.performance = this.analyzePerformance(code, language);
      
      // 5. 安全检查
      analysisResult.security = this.checkSecurity(code, language);

      // 缓存结果
      if (options.useCache !== false) {
        await defaultCache.set(cacheKey, analysisResult, this.CACHE_TTL);
      }

      const totalIssues = analysisResult.issues.length + analysisResult.suggestions.length;
      console.log(`✅ 代码分析完成: 发现 ${totalIssues} 个问题和建议`);

      return {
        success: true,
        data: analysisResult,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: totalIssues,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'analyzeAxmolCode', { code: code.substring(0, 100), language, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化分析规则
   */
  private initializeAnalysisRules(): void {
    // C++ 分析规则
    this.analysisRules.set('cpp_memory_leaks', {
      pattern: /new\s+\w+.*(?!delete)/g,
      severity: 'high',
      message: '可能存在内存泄漏：使用 new 但未找到对应的 delete',
      suggestion: '使用智能指针或确保正确释放内存'
    });

    this.analysisRules.set('cpp_deprecated_api', {
      patterns: [
        /CCSprite/g,
        /CCNode/g,
        /CCScene/g,
        /CCLayer/g
      ],
      severity: 'medium',
      message: '使用了已弃用的 Cocos2d-x API',
      suggestion: '更新为 Axmol 的新 API（去掉 CC 前缀）'
    });

    this.analysisRules.set('cpp_namespace_usage', {
      pattern: /using\s+namespace\s+std;/g,
      severity: 'low',
      message: '避免在头文件中使用 "using namespace std"',
      suggestion: '使用具体的命名空间或在实现文件中使用'
    });

    // Lua 分析规则
    this.analysisRules.set('lua_global_variables', {
      pattern: /^\s*\w+\s*=/gm,
      severity: 'medium',
      message: '可能使用了全局变量',
      suggestion: '使用 local 关键字声明局部变量'
    });

    this.analysisRules.set('lua_deprecated_api', {
      patterns: [
        /cc\.Sprite/g,
        /cc\.Node/g,
        /cc\.Scene/g
      ],
      severity: 'medium',
      message: '使用了已弃用的 Cocos2d-x Lua API',
      suggestion: '更新为 Axmol 的新 Lua API'
    });

    console.log(`📋 初始化了 ${this.analysisRules.size} 个分析规则`);
  }

  /**
   * 初始化最佳实践库
   */
  private initializeBestPractices(): void {
    // 内存管理最佳实践
    this.bestPracticesDb.set('memory_management', [
      {
        useCase: 'memory_management',
        category: '内存管理',
        title: '使用智能指针管理内存',
        description: '在 Axmol 中推荐使用智能指针来自动管理内存',
        recommendations: [
          '使用 std::shared_ptr 和 std::unique_ptr',
          '避免手动 new/delete',
          '利用 Axmol 的引用计数系统'
        ],
        examples: [
          {
            title: '智能指针使用示例',
            language: 'cpp',
            code: `// 推荐的做法
auto sprite = Sprite::create("player.png");
this->addChild(sprite);

// 避免的做法
Sprite* sprite = new Sprite();
// 容易忘记释放内存`,
            description: '使用 Axmol 的自动内存管理'
          }
        ],
        antiPatterns: [
          '手动 new/delete 对象',
          '忘记释放纹理和资源',
          '循环引用导致内存泄漏'
        ],
        performance: [
          {
            aspect: '内存使用',
            recommendation: '及时释放不需要的资源',
            impact: 'high',
            measurement: '内存占用减少 20-50%'
          }
        ],
        resources: []
      }
    ]);

    // 性能优化最佳实践
    this.bestPracticesDb.set('performance', [
      {
        useCase: 'performance',
        category: '性能优化',
        title: '批量渲染和对象池',
        description: '使用批量渲染和对象池来提高性能',
        recommendations: [
          '使用 SpriteBatchNode 进行批量渲染',
          '实现对象池避免频繁创建销毁',
          '合理使用纹理图集'
        ],
        examples: [
          {
            title: '批量渲染示例',
            language: 'cpp',
            code: `// 使用 SpriteBatchNode
auto batchNode = SpriteBatchNode::create("sprites.png");
this->addChild(batchNode);

for (int i = 0; i < 100; i++) {
    auto sprite = Sprite::createWithTexture(batchNode->getTexture());
    batchNode->addChild(sprite);
}`,
            description: '批量渲染多个使用相同纹理的精灵'
          }
        ],
        antiPatterns: [
          '每帧创建和销毁大量对象',
          '使用过多的绘制调用',
          '不合理的纹理使用'
        ],
        performance: [
          {
            aspect: '渲染性能',
            recommendation: '减少绘制调用次数',
            impact: 'high',
            measurement: 'FPS 提升 30-60%'
          }
        ],
        resources: []
      }
    ]);

    console.log(`💡 初始化了 ${this.bestPracticesDb.size} 个最佳实践类别`);
  }

  /**
   * 执行静态代码分析
   */
  private performStaticAnalysis(code: string, language: 'cpp' | 'lua'): CodeIssue[] {
    const issues: CodeIssue[] = [];
    const lines = code.split('\n');

    // 遍历所有分析规则
    for (const [ruleName, rule] of this.analysisRules.entries()) {
      if (!ruleName.startsWith(language)) continue;

      if (rule.pattern) {
        // 单个模式匹配
        this.findPatternIssues(code, lines, rule, issues);
      } else if (rule.patterns) {
        // 多个模式匹配
        for (const pattern of rule.patterns) {
          this.findPatternIssues(code, lines, { ...rule, pattern }, issues);
        }
      }
    }

    return issues;
  }

  /**
   * 查找模式匹配的问题
   */
  private findPatternIssues(code: string, lines: string[], rule: any, issues: CodeIssue[]): void {
    const matches = code.match(rule.pattern);
    if (matches) {
      for (const match of matches) {
        // 找到匹配的行号
        const lineNumber = this.findLineNumber(lines, match);
        
        issues.push({
          type: rule.severity === 'high' ? 'error' : rule.severity === 'medium' ? 'warning' : 'info',
          line: lineNumber,
          message: rule.message,
          rule: rule.pattern.toString(),
          severity: rule.severity
        });
      }
    }
  }

  /**
   * 查找匹配文本的行号
   */
  private findLineNumber(lines: string[], matchText: string): number {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(matchText)) {
        return i + 1;
      }
    }
    return 1;
  }

  /**
   * 生成改进建议
   */
  private generateSuggestions(code: string, language: 'cpp' | 'lua', issues: CodeIssue[]): CodeSuggestion[] {
    const suggestions: CodeSuggestion[] = [];

    // 基于发现的问题生成建议
    for (const issue of issues) {
      const suggestion = this.createSuggestionFromIssue(issue, language);
      if (suggestion) {
        suggestions.push(suggestion);
      }
    }

    // 添加通用改进建议
    suggestions.push(...this.generateGenericSuggestions(code, language));

    return suggestions;
  }

  /**
   * 从问题创建建议
   */
  private createSuggestionFromIssue(issue: CodeIssue, language: 'cpp' | 'lua'): CodeSuggestion | null {
    if (issue.message.includes('内存泄漏')) {
      return {
        line: issue.line,
        message: '使用智能指针或 Axmol 的自动内存管理',
        suggestedCode: language === 'cpp' ? 'auto sprite = Sprite::create("image.png");' : 'local sprite = cc.Sprite:create("image.png")',
        reason: '避免内存泄漏，提高代码安全性',
        category: '内存管理'
      };
    }

    if (issue.message.includes('已弃用')) {
      return {
        line: issue.line,
        message: '更新为 Axmol 的新 API',
        suggestedCode: language === 'cpp' ? '使用 ax:: 命名空间' : '使用新的 Lua API',
        reason: '保持与最新版本的兼容性',
        category: 'API 更新'
      };
    }

    return null;
  }

  /**
   * 生成通用改进建议
   */
  private generateGenericSuggestions(code: string, language: 'cpp' | 'lua'): CodeSuggestion[] {
    const suggestions: CodeSuggestion[] = [];

    if (language === 'cpp') {
      // C++ 特定建议
      if (!code.includes('#include "axmol.h"')) {
        suggestions.push({
          line: 1,
          message: '建议包含 Axmol 主头文件',
          suggestedCode: '#include "axmol.h"',
          reason: '确保可以使用所有 Axmol 功能',
          category: '头文件'
        });
      }

      if (!code.includes('USING_NS_AX')) {
        suggestions.push({
          line: 2,
          message: '建议使用 Axmol 命名空间',
          suggestedCode: 'USING_NS_AX;',
          reason: '简化代码，避免重复写 ax::',
          category: '命名空间'
        });
      }
    }

    return suggestions;
  }

  /**
   * 检查最佳实践
   */
  private checkBestPractices(code: string, language: 'cpp' | 'lua'): BestPractice[] {
    const practices: BestPractice[] = [];

    // 检查内存管理实践
    if (code.includes('new ') || code.includes('delete ')) {
      const memoryPractices = this.bestPracticesDb.get('memory_management');
      if (memoryPractices) {
        practices.push(...memoryPractices);
      }
    }

    // 检查性能相关代码
    if (code.includes('Sprite::create') || code.includes('addChild')) {
      const performancePractices = this.bestPracticesDb.get('performance');
      if (performancePractices) {
        practices.push(...performancePractices);
      }
    }

    return practices;
  }

  /**
   * 分析性能
   */
  private analyzePerformance(code: string, language: 'cpp' | 'lua'): PerformanceNote[] {
    const notes: PerformanceNote[] = [];

    // 检查循环中的对象创建
    if (code.match(/for.*{[\s\S]*?create[\s\S]*?}/)) {
      notes.push({
        aspect: '对象创建',
        recommendation: '避免在循环中频繁创建对象，考虑使用对象池',
        impact: 'high',
        measurement: '可减少 GC 压力和提高帧率'
      });
    }

    // 检查纹理使用
    if (code.includes('Texture2D::create') || code.includes('Image::create')) {
      notes.push({
        aspect: '纹理管理',
        recommendation: '使用纹理缓存和图集来优化纹理使用',
        impact: 'medium',
        measurement: '减少内存占用和绘制调用'
      });
    }

    return notes;
  }

  /**
   * 检查安全性
   */
  private checkSecurity(code: string, language: 'cpp' | 'lua'): SecurityNote[] {
    const notes: SecurityNote[] = [];

    // 检查空指针解引用
    if (code.includes('->') && !code.includes('nullptr')) {
      notes.push({
        type: 'vulnerability',
        description: '可能存在空指针解引用风险',
        severity: 'medium',
        recommendation: '在使用指针前检查是否为 nullptr'
      });
    }

    // 检查缓冲区溢出
    if (code.includes('strcpy') || code.includes('sprintf')) {
      notes.push({
        type: 'vulnerability',
        description: '使用了不安全的字符串函数',
        severity: 'high',
        recommendation: '使用安全的字符串函数如 strncpy 或 std::string'
      });
    }

    return notes;
  }

  /**
   * 计算代码哈希
   */
  private hashCode(code: string): string {
    let hash = 0;
    for (let i = 0; i < code.length; i++) {
      const char = code.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

// 导出默认代码分析服务实例
export const codeAnalysisService = new CodeAnalysisService();
