/**
 * Axmol MCP 工具集类型定义
 */

// 基础类型
export interface AxmolResource {
  type: 'official_docs' | 'wiki' | 'source' | 'example' | 'header' | 'document' | 'web' | 'community';
  title: string;
  url: string;
  content?: string;
  relevanceScore: number;
  matchedTerms: string[];
  source: string;
  timestamp?: string;
}

// 搜索结果类型
export interface SearchResults {
  officialDocs: AxmolResource[];
  wiki: AxmolResource[];
  sourceCode: AxmolResource[];
  examples: AxmolResource[];
  headers: AxmolResource[];
  docs: AxmolResource[];
  webSearch: AxmolResource[];
  community: AxmolResource[];
}

// 问题分析结果
export interface QuestionAnalysis {
  categories: string[];
  searchTerms: string[];
  questionType: 'how-to' | 'definition' | 'explanation' | 'troubleshooting' | 'example' | 'general';
  priority: string[];
  language?: 'cpp' | 'lua' | 'both';
}

// API 参考信息
export interface ApiReference {
  className: string;
  methodName?: string;
  namespace: string;
  description: string;
  parameters?: ApiParameter[];
  returnType?: string;
  examples: CodeExample[];
  relatedApis: string[];
  sourceFile: string;
  documentationUrl: string;
}

export interface ApiParameter {
  name: string;
  type: string;
  description: string;
  optional: boolean;
  defaultValue?: string;
}

// 代码示例
export interface CodeExample {
  title: string;
  language: 'cpp' | 'lua';
  code: string;
  description: string;
  platform?: string;
  version?: string;
  sourceUrl?: string;
}

// 构建问题
export interface BuildIssue {
  platform: string;
  errorMessage: string;
  errorType: 'compile' | 'link' | 'runtime' | 'configuration';
  solutions: BuildSolution[];
  relatedIssues: string[];
}

export interface BuildSolution {
  title: string;
  description: string;
  steps: string[];
  codeChanges?: CodeChange[];
  configChanges?: ConfigChange[];
  priority: 'high' | 'medium' | 'low';
  verified: boolean;
}

export interface CodeChange {
  file: string;
  lineNumber?: number;
  oldCode?: string;
  newCode: string;
  explanation: string;
}

export interface ConfigChange {
  file: string;
  setting: string;
  value: string;
  explanation: string;
}

// 迁移指南
export interface MigrationGuide {
  fromEngine: string;
  toEngine: string;
  topic: string;
  changes: MigrationChange[];
  examples: CodeExample[];
  commonIssues: BuildIssue[];
  resources: AxmolResource[];
}

export interface MigrationChange {
  category: string;
  oldApi: string;
  newApi: string;
  description: string;
  breaking: boolean;
  migrationSteps: string[];
}

// 平台特定信息
export interface PlatformInfo {
  platform: string;
  topic: string;
  requirements: string[];
  configuration: ConfigChange[];
  buildSteps: string[];
  commonIssues: BuildIssue[];
  examples: CodeExample[];
  resources: AxmolResource[];
}

// 最佳实践
export interface BestPractice {
  useCase: string;
  category: string;
  title: string;
  description: string;
  recommendations: string[];
  examples: CodeExample[];
  antiPatterns: string[];
  performance: PerformanceNote[];
  resources: AxmolResource[];
}

export interface PerformanceNote {
  aspect: string;
  recommendation: string;
  impact: 'high' | 'medium' | 'low';
  measurement?: string;
}

// 版本对比
export interface VersionComparison {
  feature: string;
  versions: string[];
  changes: VersionChange[];
  compatibility: CompatibilityInfo[];
  migrationNotes: string[];
}

export interface VersionChange {
  version: string;
  changeType: 'added' | 'modified' | 'deprecated' | 'removed';
  description: string;
  impact: 'breaking' | 'compatible' | 'enhancement';
}

export interface CompatibilityInfo {
  version: string;
  compatible: boolean;
  notes: string;
}

// 社区解决方案
export interface CommunitySolution {
  problem: string;
  solutions: Solution[];
  discussions: DiscussionThread[];
  relatedIssues: GitHubIssue[];
}

export interface Solution {
  title: string;
  author: string;
  description: string;
  code?: CodeExample[];
  votes: number;
  verified: boolean;
  sourceUrl: string;
  tags: string[];
}

export interface DiscussionThread {
  title: string;
  url: string;
  platform: 'github' | 'discord' | 'reddit' | 'stackoverflow';
  replies: number;
  lastActivity: string;
  tags: string[];
}

export interface GitHubIssue {
  number: number;
  title: string;
  state: 'open' | 'closed';
  labels: string[];
  url: string;
  createdAt: string;
  updatedAt: string;
}

// 代码分析结果
export interface CodeAnalysisResult {
  code: string;
  language: 'cpp' | 'lua';
  issues: CodeIssue[];
  suggestions: CodeSuggestion[];
  bestPractices: BestPractice[];
  performance: PerformanceNote[];
  security: SecurityNote[];
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  line: number;
  column?: number;
  message: string;
  rule: string;
  severity: 'high' | 'medium' | 'low';
}

export interface CodeSuggestion {
  line: number;
  column?: number;
  message: string;
  suggestedCode: string;
  reason: string;
  category: string;
}

export interface SecurityNote {
  type: 'vulnerability' | 'best-practice';
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  recommendation: string;
}

// 缓存相关
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

export interface CacheOptions {
  ttl?: number; // 生存时间（毫秒）
  maxSize?: number; // 最大缓存条目数
  enablePersistence?: boolean; // 是否持久化到磁盘
}

// 网络请求配置
export interface NetworkConfig {
  timeout: number;
  retries: number;
  retryDelay: number;
  userAgent: string;
  headers: Record<string, string>;
  rateLimit: {
    requests: number;
    window: number; // 时间窗口（毫秒）
  };
}

// 数据源配置
export interface DataSourceConfig {
  name: string;
  priority: number;
  enabled: boolean;
  baseUrl: string;
  apiKey?: string;
  rateLimit: {
    requests: number;
    window: number;
  };
  timeout: number;
  retries: number;
}

// 错误类型
export interface AxmolError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  source: string;
}

// 工具响应格式
export interface ToolResponse {
  success: boolean;
  data?: any;
  error?: AxmolError;
  metadata?: {
    searchTime: number;
    resultsCount: number;
    sources: string[];
    cacheHit: boolean;
  };
}

// 搜索选项
export interface SearchOptions {
  maxResults?: number;
  includeCode?: boolean;
  language?: 'cpp' | 'lua' | 'both';
  platforms?: string[];
  versions?: string[];
  sourceTypes?: string[];
  sortBy?: 'relevance' | 'date' | 'popularity';
  useCache?: boolean;
}
