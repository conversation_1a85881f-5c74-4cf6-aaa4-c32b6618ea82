/**
 * 文档搜索服务
 * 负责搜索 Axmol 官方文档和技术资料
 */

import * as cheerio from 'cheerio';
import { AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class DocumentationService {
  private readonly OFFICIAL_DOCS_BASE = 'https://axmol.dev/manual/latest';
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟缓存

  /**
   * 搜索 Axmol 文档
   */
  async searchDocumentation(
    query: string,
    sourceType: string = 'all',
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 开始搜索文档: "${query}" (类型: ${sourceType})`);

      // 生成缓存键
      const cacheKey = `docs_${query}_${sourceType}_${JSON.stringify(options)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as AxmolResource[] | null;
        if (cached) {
          console.log('✅ 从缓存获取文档搜索结果');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const results: AxmolResource[] = [];
      const sources: string[] = [];

      // 根据源类型搜索不同的文档源
      if (sourceType === 'all' || sourceType === 'official') {
        const officialResults = await this.searchOfficialDocs(query, options);
        results.push(...officialResults);
        if (officialResults.length > 0) sources.push('official_docs');
      }

      if (sourceType === 'all' || sourceType === 'wiki') {
        const wikiResults = await this.searchWikiDocs(query, options);
        results.push(...wikiResults);
        if (wikiResults.length > 0) sources.push('wiki');
      }

      if (sourceType === 'all' || sourceType === 'api') {
        const apiResults = await this.searchApiDocs(query, options);
        results.push(...apiResults);
        if (apiResults.length > 0) sources.push('api_docs');
      }

      // 按相关性排序
      const sortedResults = this.sortByRelevance(results, query);
      
      // 限制结果数量
      const maxResults = options.maxResults || 20;
      const finalResults = sortedResults.slice(0, maxResults);

      // 缓存结果
      if (options.useCache !== false && finalResults.length > 0) {
        await defaultCache.set(cacheKey, finalResults, this.CACHE_TTL);
      }

      console.log(`✅ 文档搜索完成: 找到 ${finalResults.length} 个结果`);

      return {
        success: true,
        data: finalResults,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: finalResults.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'searchDocumentation', { query, sourceType, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 搜索官方文档
   */
  private async searchOfficialDocs(query: string, options: SearchOptions): Promise<AxmolResource[]> {
    const results: AxmolResource[] = [];

    try {
      // 搜索主页内容
      const mainPageResult = await this.searchOfficialMainPage(query);
      if (mainPageResult) {
        results.push(mainPageResult);
      }

      // 搜索API页面 - 减少数量并添加超时控制
      const apiPages = await this.discoverApiPages(query);
      const limitedPages = apiPages.slice(0, 3); // 减少到3个页面

      for (const page of limitedPages) {
        try {
          const pageResult = await Promise.race([
            this.searchApiPage(page, query),
            new Promise<null>((_, reject) =>
              setTimeout(() => reject(new Error('Page search timeout')), 5000)
            )
          ]);

          if (pageResult) {
            results.push(pageResult);
          }
        } catch (error) {
          console.log(`⚠️ API页面 ${page} 搜索超时或失败`);
          continue;
        }
      }

      console.log(`📚 官方文档搜索完成: ${results.length} 个结果`);

    } catch (error) {
      console.log('⚠️ 官方文档搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return results;
  }

  /**
   * 搜索官方主页
   */
  private async searchOfficialMainPage(query: string): Promise<AxmolResource | null> {
    try {
      const response = await networkUtils.get(this.OFFICIAL_DOCS_BASE, {
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const title = 'Axmol Engine 官方API文档';
      const content = $('body').text();

      // 检查相关性
      const relevanceScore = this.calculateRelevance(content, query);
      
      if (relevanceScore > 0) {
        return {
          type: 'official_docs',
          title,
          url: this.OFFICIAL_DOCS_BASE,
          content: content.substring(0, 2000),
          relevanceScore,
          matchedTerms: this.extractMatchedTerms(content, query),
          source: 'official_docs',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      console.log('⚠️ 获取官方主页失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 发现相关的API页面
   */
  private async discoverApiPages(query: string): Promise<string[]> {
    const pages: string[] = [];
    
    // 基于查询词生成可能的API页面路径
    const queryTerms = query.toLowerCase().split(/\s+/);
    const classNameMappings: { [key: string]: string[] } = {
      'sprite': ['sprite', 'spritebatchnode', 'spriteframe'],
      'node': ['node'],
      'scene': ['scene', 'scenemgr'],
      'director': ['director'],
      'action': ['action', 'actionmanager', 'actionease'],
      'animation': ['animation', 'animate'],
      'event': ['event', 'eventlistener', 'eventdispatcher'],
      'touch': ['touch', 'eventtouch'],
      'keyboard': ['keyboard', 'eventkeyboard'],
      'mouse': ['mouse', 'eventmouse'],
      'physics': ['physicsbody', 'physicsworld', 'physicsshape'],
      'audio': ['audio', 'audioengine'],
      'ui': ['widget', 'button', 'label', 'textfield'],
      'render': ['renderer', 'rendercommand'],
      'texture': ['texture2d', 'textureatlasnode'],
      'camera': ['camera'],
      'light': ['light', 'ambientlight', 'directionallight'],
      'mesh': ['mesh', 'meshmaterial', 'meshvertexdata'],
      '3d': ['sprite3d', 'mesh', 'camera', 'light']
    };

    // 为每个查询词查找对应的类名
    queryTerms.forEach(term => {
      // 直接匹配
      if (classNameMappings[term]) {
        classNameMappings[term].forEach(className => {
          pages.push(`classax_1_1_${className}.html`);
        });
      }

      // 部分匹配
      Object.entries(classNameMappings).forEach(([key, classNames]) => {
        if (key.includes(term) || term.includes(key)) {
          classNames.forEach(className => {
            pages.push(`classax_1_1_${className}.html`);
          });
        }
      });
    });

    return [...new Set(pages)]; // 去重
  }

  /**
   * 搜索特定API页面
   */
  private async searchApiPage(page: string, query: string): Promise<AxmolResource | null> {
    try {
      const pageUrl = `${this.OFFICIAL_DOCS_BASE}/${page}`;
      
      const response = await networkUtils.get(pageUrl, {
        timeout: 8000,
        validateStatus: (status) => status === 200
      });

      const $ = cheerio.load(response.data);
      const title = $('title').text() || $('h1').first().text() || page;
      const content = $('body').text();

      // 检查页面是否有效
      if (content.includes('Page not found') || content.includes('404') || content.length < 100) {
        return null;
      }

      // 检查相关性
      const relevanceScore = this.calculateRelevance(content, query);
      
      if (relevanceScore > 0) {
        return {
          type: 'official_docs',
          title,
          url: pageUrl,
          content: content.substring(0, 2000),
          relevanceScore,
          matchedTerms: this.extractMatchedTerms(content, query),
          source: 'api_page',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      // 忽略404等常见错误
      if (!(error instanceof Error) || !error.message?.includes('404')) {
        console.log(`⚠️ API页面搜索失败 ${page}:`, error instanceof Error ? error.message : String(error));
      }
    }

    return null;
  }

  /**
   * 搜索Wiki文档
   */
  private async searchWikiDocs(query: string, options: SearchOptions): Promise<AxmolResource[]> {
    const results: AxmolResource[] = [];

    // 减少Wiki页面数量，优先搜索相关页面
    const queryLower = query.toLowerCase();
    const allWikiPages = [
      'Getting-Started', 'Sprite', 'Animation', 'Physics', 'Actions',
      'Scene-Management', 'UI-System', 'Audio', 'Input-Handling',
      'Platform-Specific', 'Performance', 'Troubleshooting'
    ];

    // 根据查询内容选择相关页面
    const relevantPages = allWikiPages.filter(page =>
      page.toLowerCase().includes(queryLower) ||
      queryLower.includes(page.toLowerCase().replace('-', ''))
    );

    const wikiPages = relevantPages.length > 0 ? relevantPages.slice(0, 3) : allWikiPages.slice(0, 3);

    try {
      const wikiBaseUrl = dataSourceManager.buildUrl('github_axmol_wiki', '');

      for (const page of wikiPages) {
        try {
          const pageUrl = `${wikiBaseUrl}/${page}`;

          // 添加超时控制
          const response = await Promise.race([
            networkUtils.get(pageUrl, {
              timeout: 5000,
              headers: networkUtils.getWikiHeaders()
            }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Wiki page timeout')), 6000)
            )
          ]) as any;

          const $ = cheerio.load(response.data);
          const content = $('article, .markdown-body, .wiki-body').text().trim();
          const title = $('h1, .page-title').first().text().trim() || page;

          const relevanceScore = this.calculateRelevance(content, query);

          if (relevanceScore > 0) {
            results.push({
              type: 'wiki',
              title,
              url: pageUrl,
              content: content.substring(0, 3000),
              relevanceScore,
              matchedTerms: this.extractMatchedTerms(content, query),
              source: 'wiki',
              timestamp: new Date().toISOString()
            });
          }

        } catch (error) {
          console.log(`⚠️ Wiki页面 ${page} 搜索超时或失败`);
          continue;
        }
      }

      console.log(`📖 Wiki文档搜索完成: ${results.length} 个结果`);

    } catch (error) {
      console.log('⚠️ Wiki文档搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 搜索API文档
   */
  private async searchApiDocs(query: string, options: SearchOptions): Promise<AxmolResource[]> {
    // 这里可以实现更专门的API文档搜索逻辑
    // 目前复用官方文档搜索
    return this.searchOfficialDocs(query, options);
  }

  /**
   * 计算内容与查询的相关性分数
   */
  private calculateRelevance(content: string, query: string): number {
    const contentLower = content.toLowerCase();
    const queryTerms = query.toLowerCase().split(/\s+/);
    let score = 0;

    queryTerms.forEach(term => {
      if (term.length < 2) return; // 忽略太短的词
      
      const regex = new RegExp(term, 'gi');
      const matches = contentLower.match(regex);
      if (matches) {
        score += matches.length;
      }
    });

    return score;
  }

  /**
   * 提取匹配的关键词
   */
  private extractMatchedTerms(content: string, query: string): string[] {
    const contentLower = content.toLowerCase();
    const queryTerms = query.toLowerCase().split(/\s+/);
    const matchedTerms: string[] = [];

    queryTerms.forEach(term => {
      if (term.length >= 2 && contentLower.includes(term)) {
        matchedTerms.push(term);
      }
    });

    return matchedTerms;
  }

  /**
   * 按相关性排序结果
   */
  private sortByRelevance(results: AxmolResource[], query: string): AxmolResource[] {
    return results.sort((a, b) => {
      // 首先按相关性分数排序
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }
      
      // 然后按匹配关键词数量排序
      if (a.matchedTerms.length !== b.matchedTerms.length) {
        return b.matchedTerms.length - a.matchedTerms.length;
      }
      
      // 最后按源类型优先级排序（官方文档优先）
      const sourcePriority = { 'official_docs': 1, 'api_page': 2, 'wiki': 3 };
      const aPriority = sourcePriority[a.source as keyof typeof sourcePriority] || 10;
      const bPriority = sourcePriority[b.source as keyof typeof sourcePriority] || 10;
      
      return aPriority - bPriority;
    });
  }

  /**
   * 获取文档搜索统计信息
   */
  async getSearchStats(): Promise<{
    totalSearches: number;
    cacheHitRate: number;
    averageResponseTime: number;
    topQueries: string[];
  }> {
    // 这里可以实现搜索统计逻辑
    return {
      totalSearches: 0,
      cacheHitRate: 0,
      averageResponseTime: 0,
      topQueries: []
    };
  }
}

// 导出默认文档搜索服务实例
export const documentationService = new DocumentationService();
