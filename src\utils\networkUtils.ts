/**
 * 网络请求工具类
 * 提供统一的网络请求接口，包含重试、超时、限流等功能
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { NetworkConfig, AxmolError } from '../types/index.js';

export class NetworkUtils {
  private config: NetworkConfig;
  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();

  constructor(config?: Partial<NetworkConfig>) {
    this.config = {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      userAgent: 'Axmol-MCP-Assistant/1.0',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      rateLimit: {
        requests: 60,
        window: 60000 // 1分钟
      },
      ...config
    };
  }

  /**
   * 执行HTTP GET请求
   */
  async get(url: string, options?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request('GET', url, options);
  }

  /**
   * 执行HTTP POST请求
   */
  async post(url: string, data?: any, options?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request('POST', url, { ...options, data });
  }

  /**
   * 通用请求方法
   */
  private async request(method: string, url: string, options?: AxiosRequestConfig): Promise<AxiosResponse> {
    // 检查限流
    await this.checkRateLimit(url);

    const config: AxiosRequestConfig = {
      method,
      url,
      timeout: this.config.timeout,
      headers: {
        'User-Agent': this.config.userAgent,
        ...this.config.headers,
        ...options?.headers
      },
      validateStatus: (status) => status < 500, // 只有5xx错误才重试
      maxContentLength: 10 * 1024 * 1024, // 10MB限制
      ...options
    };

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retries; attempt++) {
      try {
        console.log(`🌐 网络请求 (尝试 ${attempt}/${this.config.retries}): ${method} ${url}`);
        
        const response = await axios(config);
        
        if (response.status >= 200 && response.status < 300) {
          console.log(`✅ 请求成功: ${response.status} ${url}`);
          return response;
        } else if (response.status === 404) {
          throw new Error(`资源不存在: ${url}`);
        } else if (response.status === 403) {
          throw new Error(`访问被拒绝: ${url}`);
        } else if (response.status === 429) {
          throw new Error(`请求过于频繁: ${url}`);
        } else {
          throw new Error(`HTTP错误 ${response.status}: ${url}`);
        }

      } catch (error) {
        lastError = error as Error;
        const errorMsg = error instanceof Error ? error.message : String(error);
        
        console.log(`⚠️ 请求失败 (尝试 ${attempt}/${this.config.retries}): ${errorMsg}`);

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === this.config.retries) {
          break;
        }

        // 检查是否应该重试
        if (!this.shouldRetry(error as Error)) {
          break;
        }

        // 等待后重试
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1); // 指数退避
        console.log(`⏳ 等待 ${delay}ms 后重试...`);
        await this.sleep(delay);
      }
    }

    // 所有重试都失败了
    throw this.createAxmolError('NETWORK_ERROR', `网络请求失败: ${lastError?.message}`, {
      url,
      method,
      attempts: this.config.retries
    });
  }

  /**
   * 检查是否应该重试
   */
  private shouldRetry(error: Error): boolean {
    const message = error.message.toLowerCase();
    
    // 不重试的情况
    if (message.includes('404') || 
        message.includes('403') || 
        message.includes('401') ||
        message.includes('资源不存在') ||
        message.includes('访问被拒绝')) {
      return false;
    }

    // 重试的情况：网络错误、超时、5xx错误等
    return message.includes('timeout') ||
           message.includes('network') ||
           message.includes('econnreset') ||
           message.includes('enotfound') ||
           message.includes('5') && message.includes('error');
  }

  /**
   * 检查限流
   */
  private async checkRateLimit(url: string): Promise<void> {
    const domain = new URL(url).hostname;
    const now = Date.now();
    const windowStart = now - this.config.rateLimit.window;

    let rateLimitInfo = this.requestCounts.get(domain);
    
    if (!rateLimitInfo || rateLimitInfo.resetTime <= now) {
      // 重置计数器
      rateLimitInfo = {
        count: 0,
        resetTime: now + this.config.rateLimit.window
      };
      this.requestCounts.set(domain, rateLimitInfo);
    }

    if (rateLimitInfo.count >= this.config.rateLimit.requests) {
      const waitTime = rateLimitInfo.resetTime - now;
      console.log(`⏰ 达到限流限制，等待 ${waitTime}ms...`);
      await this.sleep(waitTime);
      
      // 重置计数器
      rateLimitInfo.count = 0;
      rateLimitInfo.resetTime = now + this.config.rateLimit.window;
    }

    rateLimitInfo.count++;
  }

  /**
   * 获取GitHub API请求头
   */
  getGitHubHeaders(): Record<string, string> {
    return {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': this.config.userAgent,
      'X-GitHub-Api-Version': '2022-11-28'
    };
  }

  /**
   * 获取Wiki页面请求头
   */
  getWikiHeaders(): Record<string, string> {
    return {
      'User-Agent': this.config.userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    };
  }

  /**
   * 检查URL是否可访问
   */
  async checkUrl(url: string): Promise<boolean> {
    try {
      const response = await axios.head(url, {
        timeout: 5000,
        headers: { 'User-Agent': this.config.userAgent }
      });
      return response.status >= 200 && response.status < 400;
    } catch {
      return false;
    }
  }

  /**
   * 批量检查URL
   */
  async checkUrls(urls: string[]): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const promises = urls.map(async (url) => {
      const isAccessible = await this.checkUrl(url);
      results.set(url, isAccessible);
    });

    await Promise.allSettled(promises);
    return results;
  }

  /**
   * 创建Axmol错误对象
   */
  private createAxmolError(code: string, message: string, details?: any): AxmolError {
    return {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      source: 'NetworkUtils'
    };
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<NetworkConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): NetworkConfig {
    return { ...this.config };
  }

  /**
   * 清理限流计数器
   */
  clearRateLimitCounters(): void {
    this.requestCounts.clear();
  }

  /**
   * 获取限流状态
   */
  getRateLimitStatus(): Map<string, { count: number; resetTime: number }> {
    return new Map(this.requestCounts);
  }
}

// 导出默认实例
export const networkUtils = new NetworkUtils();
