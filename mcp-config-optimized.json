{"mcpServers": {"axmol-mcp-server": {"command": "node", "args": ["D:\\Mytools\\MCP\\AxmolEngine-mcp\\dist\\index.js"], "cwd": "D:\\Mytools\\MCP\\AxmolEngine-mcp", "env": {"NODE_ENV": "production", "MCP_DEBUG": "false", "MCP_WEB_PORT": "8765", "MCP_DESKTOP_MODE": "true"}, "timeout": 600, "autoApprove": ["interactive_feedback"]}, "axmol-mcp-server-dev": {"command": "node", "args": ["D:\\Mytools\\MCP\\AxmolEngine-mcp\\monitor.js"], "cwd": "D:\\Mytools\\MCP\\AxmolEngine-mcp", "env": {"NODE_ENV": "development", "MCP_DEBUG": "true", "MCP_WEB_PORT": "8766"}, "timeout": 600, "autoApprove": ["interactive_feedback"]}}}