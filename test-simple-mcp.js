#!/usr/bin/env node

/**
 * 简单的MCP服务器功能测试
 * 直接导入和测试核心功能
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

// 模拟MCP服务器的核心功能
const AXMOL_OFFICIAL_DOCS = "https://axmol.dev/manual/latest";

/**
 * 测试官方知识库搜索功能
 */
async function testOfficialDocsIntegration() {
  console.log('🧪 测试官方知识库集成功能...\n');
  
  try {
    // 测试1: 主页访问
    console.log('📋 测试1: 访问官方知识库主页');
    const mainPageResponse = await axios.get(AXMOL_OFFICIAL_DOCS, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Axmol-MCP-Test',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });
    
    let mainPageContent = '';
    if (mainPageResponse.status === 200) {
      console.log('✅ 主页访问成功');
      const $ = cheerio.load(mainPageResponse.data);
      const title = $('title').text();
      console.log(`📄 页面标题: ${title}`);

      const content = $('body').text();
      mainPageContent = content; // 保存内容供后续使用
      const hasAxmolContent = content.toLowerCase().includes('axmol');
      console.log(`🔍 包含Axmol内容: ${hasAxmolContent ? '✅' : '❌'}`);
    }
    
    // 测试2: API页面访问
    console.log('\n📋 测试2: 访问已知的API页面');
    const knownApiPages = [
      'd4/d72/classax_1_1_director.html',
      'd5/db6/classax_1_1_event.html',
      'd1/dfb/classax_1_1_mesh_material.html'
    ];
    
    let successCount = 0;
    for (const page of knownApiPages) {
      try {
        const pageUrl = `${AXMOL_OFFICIAL_DOCS}/${page}`;
        const response = await axios.get(pageUrl, {
          timeout: 8000,
          headers: {
            'User-Agent': 'Axmol-MCP-Test',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
          }
        });
        
        if (response.status === 200) {
          const $ = cheerio.load(response.data);
          const title = $('title').text();
          console.log(`✅ ${page}: ${title}`);
          successCount++;
        }
      } catch (error) {
        console.log(`❌ ${page}: ${error.message}`);
      }
    }
    
    console.log(`\n📊 API页面访问成功率: ${successCount}/${knownApiPages.length}`);
    
    // 测试3: 搜索功能模拟
    console.log('\n📋 测试3: 模拟搜索功能');
    const searchTerms = ['sprite', 'director', 'event'];
    
    for (const term of searchTerms) {
      console.log(`\n🔍 搜索关键词: ${term}`);
      
      // 检查主页是否包含该关键词
      const foundInMain = mainPageContent.toLowerCase().includes(term.toLowerCase());
      console.log(`  主页匹配: ${foundInMain ? '✅' : '❌'}`);
      
      // 检查API页面是否包含该关键词
      let apiMatches = 0;
      for (const page of knownApiPages) {
        try {
          const pageUrl = `${AXMOL_OFFICIAL_DOCS}/${page}`;
          const response = await axios.get(pageUrl, {
            timeout: 5000,
            headers: { 'User-Agent': 'Axmol-MCP-Test' }
          });
          
          const $ = cheerio.load(response.data);
          const content = $('body').text().toLowerCase();
          if (content.includes(term.toLowerCase())) {
            apiMatches++;
          }
        } catch (error) {
          // 忽略错误，继续检查其他页面
        }
      }
      
      console.log(`  API页面匹配: ${apiMatches}/${knownApiPages.length}`);
    }
    
    // 测试4: 集成功能验证
    console.log('\n📋 测试4: 集成功能验证');
    
    const integrationChecks = [
      {
        name: '官方知识库URL配置',
        check: () => AXMOL_OFFICIAL_DOCS === 'https://axmol.dev/manual/latest',
        result: null
      },
      {
        name: '网络连接可用性',
        check: () => successCount > 0,
        result: null
      },
      {
        name: '内容解析能力',
        check: () => mainPageResponse.data && mainPageResponse.data.length > 1000,
        result: null
      },
      {
        name: 'API页面发现能力',
        check: () => successCount >= knownApiPages.length / 2,
        result: null
      }
    ];
    
    integrationChecks.forEach(check => {
      try {
        check.result = check.check();
        console.log(`${check.result ? '✅' : '❌'} ${check.name}`);
      } catch (error) {
        check.result = false;
        console.log(`❌ ${check.name}: ${error.message}`);
      }
    });
    
    const passedChecks = integrationChecks.filter(check => check.result).length;
    const totalChecks = integrationChecks.length;
    
    console.log(`\n📊 集成功能验证结果: ${passedChecks}/${totalChecks} 通过`);
    
    if (passedChecks === totalChecks) {
      console.log('🎉 官方知识库集成功能完全正常！');
    } else if (passedChecks >= totalChecks * 0.75) {
      console.log('✅ 官方知识库集成功能基本正常，有少量问题需要关注。');
    } else {
      console.log('⚠️ 官方知识库集成功能存在问题，需要进一步调试。');
    }
    
  } catch (error) {
    console.log(`❌ 测试过程中出现错误: ${error.message}`);
  }
}

/**
 * 测试MCP工具配置
 */
async function testMCPConfiguration() {
  console.log('\n🔧 测试MCP工具配置...');
  
  try {
    // 检查package.json
    const fs = await import('fs');
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    console.log(`✅ 项目名称: ${packageJson.name}`);
    console.log(`✅ 项目版本: ${packageJson.version}`);
    console.log(`✅ 主入口文件: ${packageJson.main}`);
    
    // 检查依赖
    const requiredDeps = ['axios', 'cheerio', '@modelcontextprotocol/sdk'];
    const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
    
    if (missingDeps.length === 0) {
      console.log('✅ 所有必需依赖都已安装');
    } else {
      console.log(`❌ 缺少依赖: ${missingDeps.join(', ')}`);
    }
    
    // 检查构建文件
    const buildFiles = ['dist/index.js', 'dist/index.d.ts'];
    const existingFiles = buildFiles.filter(file => fs.existsSync(file));
    
    console.log(`✅ 构建文件: ${existingFiles.length}/${buildFiles.length} 存在`);
    
  } catch (error) {
    console.log(`❌ 配置检查失败: ${error.message}`);
  }
}

// 运行测试
async function runAllTests() {
  console.log('🚀 开始完整的集成测试...\n');
  
  await testMCPConfiguration();
  await testOfficialDocsIntegration();
  
  console.log('\n✅ 所有测试完成！');
}

runAllTests().catch(console.error);
