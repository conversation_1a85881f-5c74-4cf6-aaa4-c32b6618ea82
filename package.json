{"name": "axmol-mcp-server", "version": "1.0.0", "description": "Axmol 游戏引擎 MCP 服务器 - 提供全面的 Axmol 开发支持", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "test:basic": "node test-basic-functionality.js", "test:simple": "node test-simple-mcp.js", "test:verify": "node test-final-verification.js", "test:stability": "node test-stability.js", "test:all": "npm run test:basic && npm run test:simple && npm run test:verify", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "monitor": "node monitor.js", "prebuild": "echo '🔧 开始编译TypeScript...'", "postbuild": "echo '✅ TypeScript编译完成!'", "prestart": "npm run build", "clean": "rimraf dist logs cache", "clean:all": "npm run clean && rimraf node_modules", "verify": "npm run build && npm run test:verify", "health": "npm run build && npm run test:basic"}, "keywords": ["axmol", "game-engine", "mcp", "cocos2d-x", "game-development"], "author": "Axmol MCP Developer", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "node-fetch": "^3.3.2", "fs-extra": "^11.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/fs-extra": "^11.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}