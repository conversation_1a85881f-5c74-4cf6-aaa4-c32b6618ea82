/**
 * 数据源管理器
 * 管理多个数据源的优先级、可用性和负载均衡
 */

import { DataSourceConfig, AxmolResource } from '../types/index.js';
import { networkUtils } from './networkUtils.js';
import { errorHandler } from './errorHandler.js';

export class DataSourceManager {
  private dataSources: Map<string, DataSourceConfig> = new Map();
  private healthStatus: Map<string, { isHealthy: boolean; lastCheck: number; failureCount: number }> = new Map();
  private readonly healthCheckInterval = 5 * 60 * 1000; // 5分钟

  constructor() {
    this.initializeDataSources();
    this.startHealthChecks();
  }

  /**
   * 初始化数据源配置
   */
  private initializeDataSources(): void {
    const sources: DataSourceConfig[] = [
      {
        name: 'axmol_official_docs',
        priority: 1,
        enabled: true,
        baseUrl: 'https://axmol.dev/manual/latest',
        rateLimit: { requests: 30, window: 60000 },
        timeout: 10000,
        retries: 3
      },
      {
        name: 'github_axmol_repo',
        priority: 2,
        enabled: true,
        baseUrl: 'https://api.github.com/repos/axmolengine/axmol',
        rateLimit: { requests: 60, window: 3600000 }, // GitHub API限制
        timeout: 15000,
        retries: 2
      },
      {
        name: 'github_axmol_wiki',
        priority: 3,
        enabled: true,
        baseUrl: 'https://github.com/axmolengine/axmol/wiki',
        rateLimit: { requests: 30, window: 60000 },
        timeout: 10000,
        retries: 2
      },
      {
        name: 'github_raw_content',
        priority: 4,
        enabled: true,
        baseUrl: 'https://raw.githubusercontent.com/axmolengine/axmol',
        rateLimit: { requests: 50, window: 60000 },
        timeout: 8000,
        retries: 2
      },
      {
        name: 'web_search_fallback',
        priority: 5,
        enabled: true,
        baseUrl: 'https://www.google.com/search',
        rateLimit: { requests: 10, window: 60000 },
        timeout: 12000,
        retries: 1
      }
    ];

    sources.forEach(source => {
      this.dataSources.set(source.name, source);
      this.healthStatus.set(source.name, {
        isHealthy: true,
        lastCheck: 0,
        failureCount: 0
      });
    });

    console.log(`📊 初始化了 ${sources.length} 个数据源`);
  }

  /**
   * 获取可用的数据源列表（按优先级排序）
   */
  getAvailableDataSources(): DataSourceConfig[] {
    return Array.from(this.dataSources.values())
      .filter(source => source.enabled && this.isHealthy(source.name))
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * 获取特定数据源配置
   */
  getDataSource(name: string): DataSourceConfig | undefined {
    return this.dataSources.get(name);
  }

  /**
   * 检查数据源是否健康
   */
  isHealthy(sourceName: string): boolean {
    const health = this.healthStatus.get(sourceName);
    return health ? health.isHealthy : false;
  }

  /**
   * 标记数据源为不健康
   */
  markUnhealthy(sourceName: string, error?: any): void {
    const health = this.healthStatus.get(sourceName);
    if (health) {
      health.isHealthy = false;
      health.failureCount++;
      health.lastCheck = Date.now();
      
      console.log(`❌ 数据源 ${sourceName} 标记为不健康 (失败次数: ${health.failureCount})`);
      
      if (error) {
        errorHandler.createError(
          'DATA_SOURCE_UNHEALTHY',
          `数据源 ${sourceName} 不可用`,
          { error: error.message, failureCount: health.failureCount },
          'DataSourceManager'
        );
      }
    }
  }

  /**
   * 标记数据源为健康
   */
  markHealthy(sourceName: string): void {
    const health = this.healthStatus.get(sourceName);
    if (health) {
      const wasUnhealthy = !health.isHealthy;
      health.isHealthy = true;
      health.failureCount = 0;
      health.lastCheck = Date.now();
      
      if (wasUnhealthy) {
        console.log(`✅ 数据源 ${sourceName} 恢复健康`);
      }
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(sourceName: string): Promise<boolean> {
    const source = this.dataSources.get(sourceName);
    if (!source) return false;

    try {
      // 根据数据源类型执行不同的健康检查
      let isHealthy = false;

      switch (sourceName) {
        case 'axmol_official_docs':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'github_axmol_repo':
          isHealthy = await networkUtils.checkUrl(`${source.baseUrl}/readme`);
          break;
        
        case 'github_axmol_wiki':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'github_raw_content':
          isHealthy = await networkUtils.checkUrl(`${source.baseUrl}/dev/README.md`);
          break;
        
        default:
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
      }

      if (isHealthy) {
        this.markHealthy(sourceName);
      } else {
        this.markUnhealthy(sourceName);
      }

      return isHealthy;
    } catch (error) {
      this.markUnhealthy(sourceName, error);
      return false;
    }
  }

  /**
   * 启动定期健康检查
   */
  private startHealthChecks(): void {
    setInterval(async () => {
      console.log('🔍 开始数据源健康检查...');
      
      const checkPromises = Array.from(this.dataSources.keys()).map(async (sourceName) => {
        const health = this.healthStatus.get(sourceName);
        
        // 如果数据源不健康且距离上次检查超过健康检查间隔，则重新检查
        if (health && (!health.isHealthy || Date.now() - health.lastCheck > this.healthCheckInterval)) {
          return this.performHealthCheck(sourceName);
        }
        
        return health?.isHealthy || false;
      });

      const results = await Promise.allSettled(checkPromises);
      const healthyCount = results.filter(result => 
        result.status === 'fulfilled' && result.value
      ).length;

      console.log(`📊 健康检查完成: ${healthyCount}/${this.dataSources.size} 个数据源健康`);
    }, this.healthCheckInterval);
  }

  /**
   * 获取数据源统计信息
   */
  getStats(): {
    total: number;
    healthy: number;
    unhealthy: number;
    sources: Array<{
      name: string;
      priority: number;
      enabled: boolean;
      healthy: boolean;
      failureCount: number;
      lastCheck: string;
    }>;
  } {
    const sources = Array.from(this.dataSources.entries()).map(([name, config]) => {
      const health = this.healthStatus.get(name)!;
      return {
        name,
        priority: config.priority,
        enabled: config.enabled,
        healthy: health.isHealthy,
        failureCount: health.failureCount,
        lastCheck: health.lastCheck > 0 ? new Date(health.lastCheck).toISOString() : 'Never'
      };
    });

    const healthy = sources.filter(s => s.healthy && s.enabled).length;
    const unhealthy = sources.filter(s => !s.healthy && s.enabled).length;

    return {
      total: sources.length,
      healthy,
      unhealthy,
      sources: sources.sort((a, b) => a.priority - b.priority)
    };
  }

  /**
   * 启用数据源
   */
  enableDataSource(sourceName: string): boolean {
    const source = this.dataSources.get(sourceName);
    if (source) {
      source.enabled = true;
      console.log(`✅ 数据源 ${sourceName} 已启用`);
      return true;
    }
    return false;
  }

  /**
   * 禁用数据源
   */
  disableDataSource(sourceName: string): boolean {
    const source = this.dataSources.get(sourceName);
    if (source) {
      source.enabled = false;
      console.log(`❌ 数据源 ${sourceName} 已禁用`);
      return true;
    }
    return false;
  }

  /**
   * 更新数据源配置
   */
  updateDataSource(sourceName: string, updates: Partial<DataSourceConfig>): boolean {
    const source = this.dataSources.get(sourceName);
    if (source) {
      Object.assign(source, updates);
      console.log(`🔧 数据源 ${sourceName} 配置已更新`);
      return true;
    }
    return false;
  }

  /**
   * 获取推荐的数据源（基于健康状态和优先级）
   */
  getRecommendedDataSource(): DataSourceConfig | null {
    const available = this.getAvailableDataSources();
    return available.length > 0 ? available[0] : null;
  }

  /**
   * 获取备用数据源列表
   */
  getFallbackDataSources(excludeSource?: string): DataSourceConfig[] {
    return this.getAvailableDataSources()
      .filter(source => source.name !== excludeSource);
  }

  /**
   * 重置数据源健康状态
   */
  resetHealthStatus(sourceName?: string): void {
    if (sourceName) {
      const health = this.healthStatus.get(sourceName);
      if (health) {
        health.isHealthy = true;
        health.failureCount = 0;
        health.lastCheck = 0;
        console.log(`🔄 数据源 ${sourceName} 健康状态已重置`);
      }
    } else {
      this.healthStatus.forEach((health, name) => {
        health.isHealthy = true;
        health.failureCount = 0;
        health.lastCheck = 0;
      });
      console.log('🔄 所有数据源健康状态已重置');
    }
  }

  /**
   * 获取数据源的URL构建器
   */
  buildUrl(sourceName: string, path: string): string {
    const source = this.dataSources.get(sourceName);
    if (!source) {
      throw new Error(`未知的数据源: ${sourceName}`);
    }

    const baseUrl = source.baseUrl.endsWith('/') ? source.baseUrl.slice(0, -1) : source.baseUrl;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    return `${baseUrl}${cleanPath}`;
  }
}

// 导出默认数据源管理器实例
export const dataSourceManager = new DataSourceManager();
