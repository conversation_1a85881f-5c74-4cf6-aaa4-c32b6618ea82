/**
 * 最佳实践服务
 * 负责提供 Axmol 开发的最佳实践和架构建议
 */

import { BestPractice, PerformanceNote, CodeExample, AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';

export class BestPracticesService {
  private readonly CACHE_TTL = 2 * 60 * 60 * 1000; // 2小时缓存

  // 最佳实践知识库
  private readonly practicesDb: Map<string, BestPractice[]> = new Map();

  constructor() {
    this.initializeBestPractices();
  }

  /**
   * 获取最佳实践建议
   */
  async getBestPractices(
    useCase: string,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`💡 获取最佳实践: ${useCase}`);

      // 生成缓存键
      const cacheKey = `best_practices_${useCase}_${JSON.stringify(options)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as BestPractice[] | null;
        if (cached) {
          console.log('✅ 从缓存获取最佳实践');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      let practices: BestPractice[] = [];

      // 1. 搜索内置最佳实践库
      practices = this.searchBuiltinPractices(useCase);
      if (practices.length > 0) {
        sources.push('builtin_practices');
      }

      // 2. 搜索社区最佳实践
      const communityPractices = await this.searchCommunityPractices(useCase);
      if (communityPractices.length > 0) {
        practices.push(...communityPractices);
        sources.push('community');
      }

      // 3. 生成动态建议
      if (practices.length === 0) {
        practices = this.generateDynamicPractices(useCase);
        sources.push('generated');
      }

      // 增强最佳实践
      practices = await this.enhancePractices(practices, useCase);

      // 按相关性排序
      practices = this.sortPracticesByRelevance(practices, useCase);

      // 缓存结果
      if (options.useCache !== false && practices.length > 0) {
        await defaultCache.set(cacheKey, practices, this.CACHE_TTL);
      }

      console.log(`✅ 最佳实践获取完成: 找到 ${practices.length} 个建议`);

      return {
        success: true,
        data: practices,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: practices.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'getBestPractices', { useCase, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化最佳实践库
   */
  private initializeBestPractices(): void {
    // 游戏架构最佳实践
    this.practicesDb.set('game_architecture', [
      {
        useCase: 'game_architecture',
        category: '游戏架构',
        title: 'MVC/MVP 架构模式',
        description: '使用 MVC 或 MVP 模式组织游戏代码，分离逻辑和表现',
        recommendations: [
          '将游戏逻辑与渲染分离',
          '使用场景管理器管理不同游戏状态',
          '实现数据驱动的游戏设计',
          '使用事件系统解耦组件'
        ],
        examples: [
          {
            title: '场景管理器示例',
            language: 'cpp',
            code: `class SceneManager {
public:
    static SceneManager* getInstance();
    
    void pushScene(Scene* scene);
    void popScene();
    void replaceScene(Scene* scene);
    
private:
    std::stack<Scene*> sceneStack;
};

// 使用示例
auto gameScene = GameScene::create();
SceneManager::getInstance()->pushScene(gameScene);`,
            description: '实现场景栈管理，支持场景切换和回退'
          }
        ],
        antiPatterns: [
          '在渲染代码中混入游戏逻辑',
          '使用全局变量管理游戏状态',
          '紧耦合的组件设计'
        ],
        performance: [
          {
            aspect: '代码维护性',
            recommendation: '清晰的架构降低维护成本',
            impact: 'high',
            measurement: '开发效率提升 40-60%'
          }
        ],
        resources: []
      }
    ]);

    // 性能优化最佳实践
    this.practicesDb.set('performance', [
      {
        useCase: 'performance',
        category: '性能优化',
        title: '渲染性能优化',
        description: '通过批量渲染、纹理优化等技术提升游戏性能',
        recommendations: [
          '使用 SpriteBatchNode 进行批量渲染',
          '合理使用纹理图集减少绘制调用',
          '实现对象池避免频繁内存分配',
          '使用 LOD (Level of Detail) 技术',
          '优化着色器和材质使用'
        ],
        examples: [
          {
            title: '对象池实现',
            language: 'cpp',
            code: `template<typename T>
class ObjectPool {
public:
    T* acquire() {
        if (available.empty()) {
            return new T();
        }
        T* obj = available.back();
        available.pop_back();
        return obj;
    }
    
    void release(T* obj) {
        obj->reset();
        available.push_back(obj);
    }
    
private:
    std::vector<T*> available;
};

// 使用示例
auto bulletPool = ObjectPool<Bullet>();
auto bullet = bulletPool.acquire();`,
            description: '对象池减少内存分配开销'
          }
        ],
        antiPatterns: [
          '每帧创建和销毁大量对象',
          '使用过多的绘制调用',
          '不合理的纹理大小和格式'
        ],
        performance: [
          {
            aspect: '帧率',
            recommendation: '减少绘制调用和内存分配',
            impact: 'high',
            measurement: 'FPS 提升 30-80%'
          }
        ],
        resources: []
      }
    ]);

    // 内存管理最佳实践
    this.practicesDb.set('memory_management', [
      {
        useCase: 'memory_management',
        category: '内存管理',
        title: '智能内存管理',
        description: '使用 Axmol 的自动内存管理和智能指针',
        recommendations: [
          '优先使用 Axmol 的 create 方法',
          '正确使用 retain/release 机制',
          '避免循环引用',
          '及时释放大型资源',
          '使用弱引用打破循环依赖'
        ],
        examples: [
          {
            title: '正确的内存管理',
            language: 'cpp',
            code: `// 推荐做法
auto sprite = Sprite::create("player.png");
this->addChild(sprite); // 自动管理内存

// 避免的做法
Sprite* sprite = new Sprite();
sprite->initWithFile("player.png");
// 容易忘记释放

// 处理循环引用
class Player : public Node {
    WeakPtr<GameScene> scene; // 使用弱引用
};`,
            description: '使用 Axmol 的自动内存管理避免内存泄漏'
          }
        ],
        antiPatterns: [
          '手动 new/delete 对象',
          '忘记释放纹理和音频资源',
          '创建循环引用'
        ],
        performance: [
          {
            aspect: '内存使用',
            recommendation: '及时释放不需要的资源',
            impact: 'high',
            measurement: '内存占用减少 20-50%'
          }
        ],
        resources: []
      }
    ]);

    // 移动平台优化
    this.practicesDb.set('mobile_optimization', [
      {
        useCase: 'mobile_optimization',
        category: '移动平台优化',
        title: '移动设备性能优化',
        description: '针对移动设备的特殊优化策略',
        recommendations: [
          '使用压缩纹理格式 (ETC2, ASTC)',
          '实现动态分辨率调整',
          '优化电池使用和发热控制',
          '使用异步加载避免卡顿',
          '实现智能的垃圾回收策略'
        ],
        examples: [
          {
            title: '纹理压缩示例',
            language: 'cpp',
            code: `// 根据平台选择合适的纹理格式
#if AX_PLATFORM == AX_PLATFORM_ANDROID
    auto texture = Texture2D::create("image.etc2");
#elif AX_PLATFORM == AX_PLATFORM_IOS
    auto texture = Texture2D::create("image.astc");
#else
    auto texture = Texture2D::create("image.png");
#endif`,
            description: '根据平台选择最优的纹理格式'
          }
        ],
        antiPatterns: [
          '使用未压缩的大尺寸纹理',
          '同步加载大型资源',
          '忽略设备性能差异'
        ],
        performance: [
          {
            aspect: '加载时间',
            recommendation: '使用压缩纹理和异步加载',
            impact: 'high',
            measurement: '加载时间减少 50-70%'
          }
        ],
        resources: []
      }
    ]);

    // 跨平台开发
    this.practicesDb.set('cross_platform', [
      {
        useCase: 'cross_platform',
        category: '跨平台开发',
        title: '跨平台兼容性最佳实践',
        description: '确保游戏在不同平台上的一致性和性能',
        recommendations: [
          '使用平台抽象层处理差异',
          '实现自适应的 UI 布局',
          '处理不同的输入方式',
          '优化不同平台的资源使用',
          '实现平台特定的功能'
        ],
        examples: [
          {
            title: '平台适配示例',
            language: 'cpp',
            code: `class PlatformAdapter {
public:
    static bool isMobile() {
#if AX_PLATFORM == AX_PLATFORM_ANDROID || AX_PLATFORM == AX_PLATFORM_IOS
        return true;
#else
        return false;
#endif
    }
    
    static Vec2 getScreenSize() {
        auto director = Director::getInstance();
        return director->getVisibleSize();
    }
};`,
            description: '创建平台适配器处理平台差异'
          }
        ],
        antiPatterns: [
          '硬编码平台特定的值',
          '忽略不同平台的性能差异',
          '使用平台特定的 API 而不做抽象'
        ],
        performance: [
          {
            aspect: '兼容性',
            recommendation: '使用平台抽象和自适应设计',
            impact: 'medium',
            measurement: '减少平台特定问题 80%'
          }
        ],
        resources: []
      }
    ]);

    console.log(`💡 初始化了 ${this.practicesDb.size} 个最佳实践类别`);
  }

  /**
   * 搜索内置最佳实践
   */
  private searchBuiltinPractices(useCase: string): BestPractice[] {
    const practices: BestPractice[] = [];
    const useCaseLower = useCase.toLowerCase();

    // 精确匹配
    const exactMatch = this.practicesDb.get(useCaseLower);
    if (exactMatch) {
      practices.push(...exactMatch);
    }

    // 模糊匹配
    for (const [key, categoryPractices] of this.practicesDb.entries()) {
      if (key !== useCaseLower && (key.includes(useCaseLower) || useCaseLower.includes(key))) {
        practices.push(...categoryPractices);
      }
    }

    // 关键词匹配
    const keywords = this.extractUseCaseKeywords(useCase);
    for (const [key, categoryPractices] of this.practicesDb.entries()) {
      if (keywords.some(keyword => key.includes(keyword))) {
        practices.push(...categoryPractices.filter(p => 
          !practices.some(existing => existing.title === p.title)
        ));
      }
    }

    return practices;
  }

  /**
   * 提取用例关键词
   */
  private extractUseCaseKeywords(useCase: string): string[] {
    const keywords: string[] = [];
    const useCaseLower = useCase.toLowerCase();

    // 性能相关关键词
    if (useCaseLower.includes('performance') || useCaseLower.includes('optimization') || 
        useCaseLower.includes('fps') || useCaseLower.includes('speed')) {
      keywords.push('performance');
    }

    // 内存相关关键词
    if (useCaseLower.includes('memory') || useCaseLower.includes('leak') || 
        useCaseLower.includes('allocation')) {
      keywords.push('memory');
    }

    // 架构相关关键词
    if (useCaseLower.includes('architecture') || useCaseLower.includes('design') || 
        useCaseLower.includes('pattern')) {
      keywords.push('architecture');
    }

    // 移动平台关键词
    if (useCaseLower.includes('mobile') || useCaseLower.includes('android') || 
        useCaseLower.includes('ios')) {
      keywords.push('mobile');
    }

    // 跨平台关键词
    if (useCaseLower.includes('cross') || useCaseLower.includes('platform') || 
        useCaseLower.includes('compatibility')) {
      keywords.push('cross_platform');
    }

    return keywords;
  }

  /**
   * 搜索社区最佳实践
   */
  private async searchCommunityPractices(useCase: string): Promise<BestPractice[]> {
    // 这里可以实现搜索社区博客、论坛等的最佳实践
    // 目前返回空数组，后续可以扩展
    return [];
  }

  /**
   * 生成动态最佳实践
   */
  private generateDynamicPractices(useCase: string): BestPractice[] {
    const practices: BestPractice[] = [];

    // 基于用例生成通用建议
    practices.push({
      useCase,
      category: '通用建议',
      title: `${useCase} 开发最佳实践`,
      description: `针对 ${useCase} 的通用开发建议和注意事项`,
      recommendations: [
        '遵循 Axmol 的编码规范',
        '使用版本控制管理代码',
        '编写单元测试确保代码质量',
        '定期进行代码审查',
        '关注性能和内存使用'
      ],
      examples: [
        {
          title: '基础代码结构',
          language: 'cpp',
          code: `// 良好的代码结构示例
class ${this.capitalize(useCase)}Manager : public Node {
public:
    static ${this.capitalize(useCase)}Manager* create();
    virtual bool init() override;
    
    // 公共接口
    void start();
    void stop();
    void update(float dt);
    
private:
    // 私有成员和方法
    void initializeComponents();
    void cleanup();
};`,
          description: `${useCase} 的基础代码结构示例`
        }
      ],
      antiPatterns: [
        '忽略错误处理',
        '使用魔法数字',
        '过度复杂的设计'
      ],
      performance: [
        {
          aspect: '代码质量',
          recommendation: '遵循最佳实践提高代码质量',
          impact: 'medium',
          measurement: '减少 bug 数量 30-50%'
        }
      ],
      resources: []
    });

    return practices;
  }

  /**
   * 首字母大写
   */
  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * 增强最佳实践
   */
  private async enhancePractices(practices: BestPractice[], useCase: string): Promise<BestPractice[]> {
    // 为每个实践添加相关资源
    for (const practice of practices) {
      if (practice.resources.length === 0) {
        practice.resources = await this.findRelatedResources(practice, useCase);
      }
    }

    return practices;
  }

  /**
   * 查找相关资源
   */
  private async findRelatedResources(practice: BestPractice, useCase: string): Promise<AxmolResource[]> {
    const resources: AxmolResource[] = [
      {
        type: 'official_docs',
        title: 'Axmol 官方文档',
        url: 'https://axmol.dev/manual/latest',
        content: 'Axmol 引擎的官方文档',
        relevanceScore: 8,
        matchedTerms: [useCase, practice.category],
        source: 'official'
      },
      {
        type: 'wiki',
        title: 'Axmol GitHub Wiki',
        url: 'https://github.com/axmolengine/axmol/wiki',
        content: 'Axmol 引擎的 GitHub Wiki',
        relevanceScore: 7,
        matchedTerms: [useCase],
        source: 'community'
      }
    ];

    return resources;
  }

  /**
   * 按相关性排序最佳实践
   */
  private sortPracticesByRelevance(practices: BestPractice[], useCase: string): BestPractice[] {
    return practices.sort((a, b) => {
      // 计算相关性分数
      const aScore = this.calculateRelevanceScore(a, useCase);
      const bScore = this.calculateRelevanceScore(b, useCase);
      
      return bScore - aScore;
    });
  }

  /**
   * 计算相关性分数
   */
  private calculateRelevanceScore(practice: BestPractice, useCase: string): number {
    let score = 0;
    const useCaseLower = useCase.toLowerCase();

    // 用例名称匹配
    if (practice.useCase.toLowerCase() === useCaseLower) {
      score += 10;
    } else if (practice.useCase.toLowerCase().includes(useCaseLower)) {
      score += 5;
    }

    // 标题匹配
    if (practice.title.toLowerCase().includes(useCaseLower)) {
      score += 3;
    }

    // 描述匹配
    if (practice.description.toLowerCase().includes(useCaseLower)) {
      score += 2;
    }

    // 性能影响权重
    const highImpactCount = practice.performance.filter(p => p.impact === 'high').length;
    score += highImpactCount * 2;

    return score;
  }
}

// 导出默认最佳实践服务实例
export const bestPracticesService = new BestPracticesService();
