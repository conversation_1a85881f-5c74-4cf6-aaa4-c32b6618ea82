{"parser": "@typescript-eslint/parser", "extends": ["eslint:recommended"], "env": {"node": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json"}, "rules": {"no-console": "off", "prefer-const": "error", "no-var": "error", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}, "ignorePatterns": ["dist/", "node_modules/", "*.js", "*.d.ts"]}