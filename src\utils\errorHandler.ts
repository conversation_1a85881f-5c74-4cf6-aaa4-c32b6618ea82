/**
 * 错误处理工具类
 * 提供统一的错误处理、日志记录和错误恢复机制
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import { AxmolError } from '../types/index.js';

export class ErrorHandler {
  private errorLog: AxmolError[] = [];
  private maxLogSize: number = 1000;
  private logFile: string;

  constructor() {
    this.logFile = path.join(process.cwd(), 'logs', 'errors.json');
    this.initErrorLog();
  }

  /**
   * 创建标准化的Axmol错误
   */
  createError(
    code: string,
    message: string,
    details?: any,
    source: string = 'Unknown'
  ): AxmolError {
    const error: AxmolError = {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      source
    };

    this.logError(error);
    return error;
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error: any, url: string, source: string): AxmolError {
    let code = 'NETWORK_ERROR';
    let message = '网络请求失败';

    if (error.code === 'ENOTFOUND') {
      code = 'DNS_ERROR';
      message = 'DNS解析失败，请检查网络连接';
    } else if (error.code === 'ECONNRESET') {
      code = 'CONNECTION_RESET';
      message = '连接被重置，可能是网络不稳定';
    } else if (error.code === 'ETIMEDOUT') {
      code = 'TIMEOUT_ERROR';
      message = '请求超时，请稍后重试';
    } else if (error.response?.status === 404) {
      code = 'NOT_FOUND';
      message = '请求的资源不存在';
    } else if (error.response?.status === 403) {
      code = 'ACCESS_DENIED';
      message = '访问被拒绝，可能需要认证';
    } else if (error.response?.status === 429) {
      code = 'RATE_LIMITED';
      message = '请求过于频繁，请稍后重试';
    } else if (error.response?.status >= 500) {
      code = 'SERVER_ERROR';
      message = '服务器错误，请稍后重试';
    }

    return this.createError(code, message, {
      originalError: error.message,
      url,
      statusCode: error.response?.status,
      statusText: error.response?.statusText
    }, source);
  }

  /**
   * 处理解析错误
   */
  handleParseError(error: any, data: string, source: string): AxmolError {
    return this.createError('PARSE_ERROR', '数据解析失败', {
      originalError: error.message,
      dataLength: data.length,
      dataPreview: data.substring(0, 200)
    }, source);
  }

  /**
   * 处理缓存错误
   */
  handleCacheError(error: any, operation: string, key: string): AxmolError {
    return this.createError('CACHE_ERROR', `缓存操作失败: ${operation}`, {
      originalError: error.message,
      operation,
      key
    }, 'CacheUtils');
  }

  /**
   * 处理文件系统错误
   */
  handleFileSystemError(error: any, operation: string, path: string): AxmolError {
    let code = 'FILE_SYSTEM_ERROR';
    let message = `文件系统操作失败: ${operation}`;

    if (error.code === 'ENOENT') {
      code = 'FILE_NOT_FOUND';
      message = '文件或目录不存在';
    } else if (error.code === 'EACCES') {
      code = 'PERMISSION_DENIED';
      message = '权限不足，无法访问文件';
    } else if (error.code === 'ENOSPC') {
      code = 'DISK_FULL';
      message = '磁盘空间不足';
    }

    return this.createError(code, message, {
      originalError: error.message,
      operation,
      path,
      errorCode: error.code
    }, 'FileSystem');
  }

  /**
   * 处理API错误
   */
  handleApiError(error: any, apiName: string, params: any): AxmolError {
    return this.createError('API_ERROR', `API调用失败: ${apiName}`, {
      originalError: error.message,
      apiName,
      params
    }, 'ApiService');
  }

  /**
   * 记录错误
   */
  private logError(error: AxmolError): void {
    // 添加到内存日志
    this.errorLog.push(error);

    // 限制内存日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize);
    }

    // 输出到控制台
    console.error(`❌ [${error.source}] ${error.code}: ${error.message}`);
    if (error.details) {
      console.error('   详情:', JSON.stringify(error.details, null, 2));
    }

    // 异步写入文件
    this.writeErrorToFile(error).catch(err => {
      console.error('写入错误日志失败:', err.message);
    });
  }

  /**
   * 写入错误到文件
   */
  private async writeErrorToFile(error: AxmolError): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.logFile));
      
      // 读取现有日志
      let existingErrors: AxmolError[] = [];
      if (await fs.pathExists(this.logFile)) {
        try {
          existingErrors = await fs.readJson(this.logFile);
        } catch {
          // 如果读取失败，从空数组开始
          existingErrors = [];
        }
      }

      // 添加新错误
      existingErrors.push(error);

      // 限制文件中的错误数量
      if (existingErrors.length > this.maxLogSize) {
        existingErrors = existingErrors.slice(-this.maxLogSize);
      }

      // 写入文件
      await fs.writeJson(this.logFile, existingErrors, { spaces: 2 });
    } catch (err) {
      console.error('写入错误日志文件失败:', err);
    }
  }

  /**
   * 获取最近的错误
   */
  getRecentErrors(count: number = 10): AxmolError[] {
    return this.errorLog.slice(-count);
  }

  /**
   * 获取特定类型的错误
   */
  getErrorsByCode(code: string): AxmolError[] {
    return this.errorLog.filter(error => error.code === code);
  }

  /**
   * 获取特定来源的错误
   */
  getErrorsBySource(source: string): AxmolError[] {
    return this.errorLog.filter(error => error.source === source);
  }

  /**
   * 清理旧错误
   */
  clearOldErrors(olderThanHours: number = 24): number {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000).toISOString();
    const initialLength = this.errorLog.length;
    
    this.errorLog = this.errorLog.filter(error => error.timestamp > cutoffTime);
    
    const removedCount = initialLength - this.errorLog.length;
    if (removedCount > 0) {
      console.log(`🧹 清理了 ${removedCount} 个旧错误记录`);
    }
    
    return removedCount;
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byCode: Record<string, number>;
    bySource: Record<string, number>;
    recentCount: number; // 最近1小时的错误数
  } {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const byCode: Record<string, number> = {};
    const bySource: Record<string, number> = {};
    let recentCount = 0;

    this.errorLog.forEach(error => {
      // 按错误码统计
      byCode[error.code] = (byCode[error.code] || 0) + 1;
      
      // 按来源统计
      bySource[error.source] = (bySource[error.source] || 0) + 1;
      
      // 统计最近错误
      if (error.timestamp > oneHourAgo) {
        recentCount++;
      }
    });

    return {
      total: this.errorLog.length,
      byCode,
      bySource,
      recentCount
    };
  }

  /**
   * 初始化错误日志
   */
  private async initErrorLog(): Promise<void> {
    try {
      if (await fs.pathExists(this.logFile)) {
        const existingErrors = await fs.readJson(this.logFile);
        if (Array.isArray(existingErrors)) {
          this.errorLog = existingErrors.slice(-this.maxLogSize);
          console.log(`📋 加载了 ${this.errorLog.length} 个历史错误记录`);
        }
      }
    } catch (error) {
      console.error('初始化错误日志失败:', error);
    }
  }

  /**
   * 导出错误日志
   */
  async exportErrorLog(filePath?: string): Promise<string> {
    const exportPath = filePath || path.join(process.cwd(), 'logs', `errors_export_${Date.now()}.json`);
    
    try {
      await fs.ensureDir(path.dirname(exportPath));
      await fs.writeJson(exportPath, {
        exportTime: new Date().toISOString(),
        totalErrors: this.errorLog.length,
        errors: this.errorLog,
        stats: this.getErrorStats()
      }, { spaces: 2 });
      
      console.log(`📤 错误日志已导出到: ${exportPath}`);
      return exportPath;
    } catch (error) {
      throw this.handleFileSystemError(error, 'export', exportPath);
    }
  }

  /**
   * 检查系统健康状态
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    details: any;
  } {
    const stats = this.getErrorStats();
    const recentErrorRate = stats.recentCount;

    if (recentErrorRate === 0) {
      return {
        status: 'healthy',
        message: '系统运行正常',
        details: stats
      };
    } else if (recentErrorRate < 10) {
      return {
        status: 'warning',
        message: `最近1小时有 ${recentErrorRate} 个错误`,
        details: stats
      };
    } else {
      return {
        status: 'critical',
        message: `最近1小时有 ${recentErrorRate} 个错误，系统可能存在问题`,
        details: stats
      };
    }
  }
}

// 导出默认错误处理器实例
export const errorHandler = new ErrorHandler();
