# 🚀 Axmol MCP 智能助手使用指南

## 📋 快速开始

### 1. 验证安装
```bash
# 检查项目版本
npm list

# 验证构建产物
ls dist/

# 预期输出：
# index.js  index.d.ts
```

### 2. 验证官方知识库集成功能
```bash
# 运行官方知识库集成测试
node test-simple-mcp.js

# 预期结果：
# ✅ 主页访问成功
# ✅ API页面访问成功率: 3/3 或更高
# ✅ 集成功能验证结果: 4/4 通过
# 🎉 官方知识库集成功能完全正常！
```

### 3. 验证MCP服务器功能
```bash
# 运行MCP功能测试
node test-mcp-functionality.js

# 预期结果：
# ✅ 构建产物验证通过
# ✅ 官方知识库集成功能正常
# ✅ MCP服务器可以正常启动和响应
# ✅ 项目已达到生产就绪状态
```

## 🏛️ 官方知识库集成功能

### 核心特性
- **🔗 直接访问**: 实时访问 https://axmol.dev/manual/latest/
- **🔍 智能发现**: 自动发现相关API页面
- **📊 相关性评分**: 基于搜索词匹配度排序
- **🔄 智能重试**: 网络异常时自动重试
- **⚡ 批量处理**: 并发访问多个API页面

### 数据源优先级
1. **🏛️ 官方API文档** (最高优先级)
2. **📚 官方Wiki文档**
3. **💻 GitHub源码**
4. **🎯 示例代码**
5. **🌐 网络搜索** (备用)

## 💡 使用建议

### 在MCP客户端中使用
1. **配置MCP服务器**：
   ```json
   {
     "mcpServers": {
       "axmol-assistant": {
         "command": "node",
         "args": ["path/to/AxmolEngine-mcp/dist/index.js"]
       }
     }
   }
   ```

2. **提问示例**：
   - "如何在Axmol中创建精灵？"
   - "Director类的作用是什么？"
   - "如何处理触摸事件？"
   - "Axmol中的动画系统怎么使用？"

3. **预期响应特点**：
   - 🏛️ **官方API文档**部分会优先显示
   - 包含来自 axmol.dev 的权威信息
   - 提供完整的代码示例
   - 明确标注信息来源

### 验证官方知识库功能
查看响应中是否包含以下标识：
- ✅ "🏛️ 官方API文档 (最权威)" 部分
- ✅ "axmol.dev/manual/latest" 链接
- ✅ "官方知识库" 或 "官方API文档" 关键词
- ✅ 高相关性分数的API页面信息

## 🔧 故障排除

### 常见问题

1. **网络连接问题**
   ```bash
   # 测试网络连接
   curl -I https://axmol.dev/manual/latest/
   
   # 预期：HTTP/2 200
   ```

2. **构建问题**
   ```bash
   # 清理并重新构建
   rm -rf dist/
   npm run build
   ```

3. **依赖问题**
   ```bash
   # 重新安装依赖
   rm -rf node_modules/
   npm install
   ```

### 调试模式
```bash
# 启用详细日志
DEBUG=axmol-mcp node dist/index.js

# 或者查看测试输出
node test-simple-mcp.js 2>&1 | tee debug.log
```

## 📊 性能指标

### 预期性能
- **官方API文档访问**: < 5秒
- **API页面发现**: 3-8个相关页面
- **搜索响应时间**: < 30秒
- **成功率**: > 90%

### 监控指标
- 官方知识库访问成功率
- API页面发现数量
- 搜索结果相关性分数
- 网络请求重试次数

## 🎯 最佳实践

### 提问技巧
1. **具体明确**: "如何创建精灵" 比 "精灵问题" 更好
2. **包含上下文**: 说明你的开发场景和需求
3. **指定语言**: 明确需要C++还是Lua代码示例
4. **分步提问**: 复杂问题可以分解为多个简单问题

### 结果验证
1. **检查数据源**: 优先参考官方API文档部分
2. **验证链接**: 点击axmol.dev链接确认信息准确性
3. **测试代码**: 在实际项目中验证提供的代码示例
4. **交叉验证**: 对比多个数据源的信息

## 🔄 更新和维护

### 定期检查
```bash
# 每周运行一次验证测试
node test-simple-mcp.js

# 检查依赖更新
npm outdated

# 更新依赖（谨慎操作）
npm update
```

### 版本管理
- 当前版本：v1.0.0 (官方知识库集成版本)
- 主要特性：官方API文档集成
- 测试状态：4/4项验证通过

---

**🎮 祝您使用愉快！如有问题，请参考项目README.md或提交Issue。**
