/**
 * 构建问题诊断服务
 * 负责诊断和解决 Axmol 构建相关问题
 */

import { BuildIssue, BuildSolution, CodeChange, ConfigChange, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class BuildIssueService {
  private readonly GITHUB_API_BASE = 'https://api.github.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1小时缓存

  // 常见构建问题的知识库
  private readonly knownIssues: Map<string, BuildSolution[]> = new Map();

  constructor() {
    this.initializeKnownIssues();
  }

  /**
   * 解决构建问题
   */
  async solveBuildIssue(
    platform: string,
    errorMessage: string,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔧 诊断构建问题: ${platform} - ${errorMessage.substring(0, 100)}...`);

      // 生成缓存键
      const cacheKey = `build_issue_${platform}_${this.hashString(errorMessage)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as BuildIssue | null;
        if (cached) {
          console.log('✅ 从缓存获取构建问题解决方案');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.solutions.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      const solutions: BuildSolution[] = [];

      // 1. 检查已知问题库
      const knownSolutions = this.searchKnownIssues(platform, errorMessage);
      if (knownSolutions.length > 0) {
        solutions.push(...knownSolutions);
        sources.push('known_issues');
      }

      // 2. 搜索GitHub Issues
      const githubSolutions = await this.searchGitHubIssues(platform, errorMessage);
      if (githubSolutions.length > 0) {
        solutions.push(...githubSolutions);
        sources.push('github_issues');
      }

      // 3. 搜索社区讨论
      const communitySolutions = await this.searchCommunityDiscussions(platform, errorMessage);
      if (communitySolutions.length > 0) {
        solutions.push(...communitySolutions);
        sources.push('community');
      }

      // 4. 生成通用解决方案
      if (solutions.length === 0) {
        const genericSolutions = this.generateGenericSolutions(platform, errorMessage);
        solutions.push(...genericSolutions);
        sources.push('generic');
      }

      // 按优先级排序解决方案
      const sortedSolutions = this.sortSolutionsByPriority(solutions);

      const buildIssue: BuildIssue = {
        platform,
        errorMessage,
        errorType: this.classifyErrorType(errorMessage),
        solutions: sortedSolutions,
        relatedIssues: await this.findRelatedIssues(platform, errorMessage)
      };

      // 缓存结果
      if (options.useCache !== false && solutions.length > 0) {
        await defaultCache.set(cacheKey, buildIssue, this.CACHE_TTL);
      }

      console.log(`✅ 构建问题诊断完成: 找到 ${solutions.length} 个解决方案`);

      return {
        success: true,
        data: buildIssue,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: solutions.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'solveBuildIssue', { platform, errorMessage, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化已知问题库
   */
  private initializeKnownIssues(): void {
    // Android 构建问题
    this.knownIssues.set('android_ndk', [
      {
        title: 'NDK版本不兼容',
        description: 'Android NDK版本与Axmol不兼容',
        steps: [
          '检查当前NDK版本',
          '下载推荐的NDK版本（r23c或更新）',
          '更新local.properties中的ndk.dir路径',
          '清理并重新构建项目'
        ],
        codeChanges: [],
        configChanges: [
          {
            file: 'local.properties',
            setting: 'ndk.dir',
            value: '/path/to/android-ndk-r23c',
            explanation: '设置正确的NDK路径'
          }
        ],
        priority: 'high',
        verified: true
      }
    ]);

    // iOS 构建问题
    this.knownIssues.set('ios_xcode', [
      {
        title: 'Xcode版本兼容性问题',
        description: 'Xcode版本过旧或过新导致的构建失败',
        steps: [
          '检查Xcode版本要求',
          '更新到推荐的Xcode版本',
          '清理派生数据',
          '重新生成项目文件'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'high',
        verified: true
      }
    ]);

    // Windows 构建问题
    this.knownIssues.set('windows_cmake', [
      {
        title: 'CMake配置错误',
        description: 'Windows平台CMake配置问题',
        steps: [
          '检查CMake版本（需要3.16或更高）',
          '确保Visual Studio工具链正确安装',
          '删除CMakeCache.txt文件',
          '重新运行CMake配置'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: true
      }
    ]);

    console.log(`📚 初始化了 ${this.knownIssues.size} 个已知问题类别`);
  }

  /**
   * 搜索已知问题
   */
  private searchKnownIssues(platform: string, errorMessage: string): BuildSolution[] {
    const solutions: BuildSolution[] = [];
    const errorLower = errorMessage.toLowerCase();
    const platformLower = platform.toLowerCase();

    // 搜索平台特定问题
    for (const [key, issueSolutions] of this.knownIssues.entries()) {
      if (key.includes(platformLower)) {
        for (const solution of issueSolutions) {
          if (this.isErrorRelevant(errorLower, solution)) {
            solutions.push(solution);
          }
        }
      }
    }

    // 搜索通用问题
    const commonKeywords = ['cmake', 'compiler', 'linker', 'dependency', 'library'];
    for (const keyword of commonKeywords) {
      if (errorLower.includes(keyword)) {
        const keywordSolutions = this.knownIssues.get(keyword);
        if (keywordSolutions) {
          solutions.push(...keywordSolutions);
        }
      }
    }

    return solutions;
  }

  /**
   * 检查错误是否与解决方案相关
   */
  private isErrorRelevant(errorMessage: string, solution: BuildSolution): boolean {
    const solutionKeywords = [
      ...solution.title.toLowerCase().split(/\s+/),
      ...solution.description.toLowerCase().split(/\s+/)
    ];

    return solutionKeywords.some(keyword => 
      keyword.length > 3 && errorMessage.includes(keyword)
    );
  }

  /**
   * 搜索GitHub Issues
   */
  private async searchGitHubIssues(platform: string, errorMessage: string): Promise<BuildSolution[]> {
    const solutions: BuildSolution[] = [];

    try {
      // 构建搜索查询
      const searchQuery = this.buildGitHubSearchQuery(platform, errorMessage);
      const searchUrl = `${this.GITHUB_API_BASE}/search/issues?q=${encodeURIComponent(searchQuery)}&sort=updated&per_page=10`;

      const response = await networkUtils.get(searchUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 10000
      });

      const issues = response.data.items || [];

      for (const issue of issues.slice(0, 5)) {
        const solution = await this.extractSolutionFromIssue(issue);
        if (solution) {
          solutions.push(solution);
        }
      }

      console.log(`🔍 从GitHub Issues找到 ${solutions.length} 个解决方案`);

    } catch (error) {
      console.log('⚠️ GitHub Issues搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return solutions;
  }

  /**
   * 构建GitHub搜索查询
   */
  private buildGitHubSearchQuery(platform: string, errorMessage: string): string {
    const keywords = this.extractErrorKeywords(errorMessage);
    const query = [
      `repo:${this.AXMOL_REPO}`,
      `is:issue`,
      platform,
      ...keywords.slice(0, 3)
    ].join(' ');

    return query;
  }

  /**
   * 提取错误关键词
   */
  private extractErrorKeywords(errorMessage: string): string[] {
    const keywords: string[] = [];
    
    // 提取常见的错误关键词
    const errorPatterns = [
      /error\s+(\w+)/gi,
      /undefined\s+(\w+)/gi,
      /cannot\s+find\s+(\w+)/gi,
      /missing\s+(\w+)/gi,
      /failed\s+to\s+(\w+)/gi
    ];

    errorPatterns.forEach(pattern => {
      const matches = errorMessage.match(pattern);
      if (matches) {
        keywords.push(...matches);
      }
    });

    // 添加文件扩展名和路径信息
    const fileMatches = errorMessage.match(/\w+\.(h|cpp|c|hpp|cc)/g);
    if (fileMatches) {
      keywords.push(...fileMatches);
    }

    return [...new Set(keywords)].slice(0, 5);
  }

  /**
   * 从Issue中提取解决方案
   */
  private async extractSolutionFromIssue(issue: any): Promise<BuildSolution | null> {
    try {
      // 检查Issue是否已关闭（通常意味着已解决）
      if (issue.state !== 'closed') {
        return null;
      }

      return {
        title: `GitHub Issue解决方案: ${issue.title}`,
        description: issue.body?.substring(0, 200) || '来自GitHub Issue的解决方案',
        steps: [
          '查看完整的Issue讨论',
          '按照Issue中提到的解决步骤操作',
          '如果问题仍然存在，可以在Issue中留言求助'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: false
      };

    } catch (error) {
      return null;
    }
  }

  /**
   * 搜索社区讨论
   */
  private async searchCommunityDiscussions(platform: string, errorMessage: string): Promise<BuildSolution[]> {
    // 这里可以实现搜索Discord、Reddit等社区平台的逻辑
    // 目前返回空数组，后续可以扩展
    return [];
  }

  /**
   * 生成通用解决方案
   */
  private generateGenericSolutions(platform: string, errorMessage: string): BuildSolution[] {
    const solutions: BuildSolution[] = [];
    const errorType = this.classifyErrorType(errorMessage);

    switch (errorType) {
      case 'compile':
        solutions.push({
          title: '编译错误通用解决方案',
          description: '常见的编译错误解决步骤',
          steps: [
            '检查代码语法错误',
            '确保所有头文件路径正确',
            '检查编译器版本兼容性',
            '清理并重新构建项目'
          ],
          codeChanges: [],
          configChanges: [],
          priority: 'low',
          verified: false
        });
        break;

      case 'link':
        solutions.push({
          title: '链接错误通用解决方案',
          description: '常见的链接错误解决步骤',
          steps: [
            '检查库文件是否存在',
            '确认库文件路径配置正确',
            '检查库文件版本兼容性',
            '重新生成项目文件'
          ],
          codeChanges: [],
          configChanges: [],
          priority: 'low',
          verified: false
        });
        break;

      default:
        solutions.push({
          title: '通用构建问题解决方案',
          description: '适用于大多数构建问题的通用步骤',
          steps: [
            '清理构建缓存',
            '检查依赖项是否正确安装',
            '确认开发环境配置',
            '查看详细的错误日志',
            '搜索相关的GitHub Issues'
          ],
          codeChanges: [],
          configChanges: [],
          priority: 'low',
          verified: false
        });
    }

    return solutions;
  }

  /**
   * 分类错误类型
   */
  private classifyErrorType(errorMessage: string): 'compile' | 'link' | 'runtime' | 'configuration' {
    const errorLower = errorMessage.toLowerCase();

    if (errorLower.includes('compile') || errorLower.includes('syntax') || errorLower.includes('parse')) {
      return 'compile';
    } else if (errorLower.includes('link') || errorLower.includes('undefined reference') || errorLower.includes('unresolved')) {
      return 'link';
    } else if (errorLower.includes('runtime') || errorLower.includes('crash') || errorLower.includes('exception')) {
      return 'runtime';
    } else {
      return 'configuration';
    }
  }

  /**
   * 按优先级排序解决方案
   */
  private sortSolutionsByPriority(solutions: BuildSolution[]): BuildSolution[] {
    const priorityOrder = { 'high': 1, 'medium': 2, 'low': 3 };
    
    return solutions.sort((a, b) => {
      // 首先按验证状态排序
      if (a.verified !== b.verified) {
        return a.verified ? -1 : 1;
      }
      
      // 然后按优先级排序
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }

  /**
   * 查找相关问题
   */
  private async findRelatedIssues(platform: string, errorMessage: string): Promise<string[]> {
    // 简化实现，返回一些通用的相关问题链接
    return [
      `https://github.com/${this.AXMOL_REPO}/issues?q=is%3Aissue+${platform}+build`,
      `https://github.com/${this.AXMOL_REPO}/wiki/Build-${platform}`
    ];
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

// 导出默认构建问题服务实例
export const buildIssueService = new BuildIssueService();
