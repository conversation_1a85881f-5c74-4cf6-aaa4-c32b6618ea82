/**
 * 平台特定信息服务
 * 负责提供各平台特定的配置、构建和问题解决信息
 */

import { PlatformInfo, ConfigChange, BuildIssue, CodeExample, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class PlatformService {
  private readonly GITHUB_RAW_BASE = 'https://raw.githubusercontent.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 2 * 60 * 60 * 1000; // 2小时缓存

  // 平台信息知识库
  private readonly platformKnowledge: Map<string, PlatformInfo> = new Map();

  constructor() {
    this.initializePlatformKnowledge();
  }

  /**
   * 获取平台特定信息
   */
  async findPlatformSpecificInfo(
    platform: string,
    topic: string,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🎯 获取平台特定信息: ${platform} - ${topic}`);

      // 生成缓存键
      const cacheKey = `platform_${platform}_${topic}_${JSON.stringify(options)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as PlatformInfo | null;
        if (cached) {
          console.log('✅ 从缓存获取平台信息');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: 1,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      let platformInfo: PlatformInfo | null = null;

      // 1. 检查内置知识库
      platformInfo = this.getBuiltinPlatformInfo(platform, topic);
      if (platformInfo) {
        sources.push('builtin_knowledge');
      }

      // 2. 搜索官方平台文档
      if (!platformInfo) {
        platformInfo = await this.searchOfficialPlatformDocs(platform, topic);
        if (platformInfo) {
          sources.push('official_docs');
        }
      }

      // 3. 搜索平台特定的构建脚本和配置
      if (!platformInfo) {
        platformInfo = await this.searchPlatformConfigs(platform, topic);
        if (platformInfo) {
          sources.push('platform_configs');
        }
      }

      // 4. 生成基础平台信息
      if (!platformInfo) {
        platformInfo = this.generateBasicPlatformInfo(platform, topic);
        sources.push('generated');
      }

      // 增强平台信息
      if (platformInfo) {
        platformInfo = await this.enhancePlatformInfo(platformInfo);
      }

      if (!platformInfo) {
        throw new Error(`未找到 ${platform} 平台的 ${topic} 相关信息`);
      }

      // 缓存结果
      if (options.useCache !== false) {
        await defaultCache.set(cacheKey, platformInfo, this.CACHE_TTL);
      }

      console.log(`✅ 平台信息获取完成: ${platform} - ${topic}`);

      return {
        success: true,
        data: platformInfo,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 1,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'findPlatformSpecificInfo', { platform, topic, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化平台知识库
   */
  private initializePlatformKnowledge(): void {
    // Android 平台信息
    this.platformKnowledge.set('android_build', {
      platform: 'Android',
      topic: 'build',
      requirements: [
        'Android Studio 4.0 或更高版本',
        'Android NDK r23c 或更高版本',
        'Android SDK API Level 21 或更高版本',
        'CMake 3.16 或更高版本',
        'Java 8 或更高版本'
      ],
      configuration: [
        {
          file: 'local.properties',
          setting: 'ndk.dir',
          value: '/path/to/android-ndk-r23c',
          explanation: '设置 Android NDK 路径'
        },
        {
          file: 'local.properties',
          setting: 'sdk.dir',
          value: '/path/to/android-sdk',
          explanation: '设置 Android SDK 路径'
        },
        {
          file: 'app/build.gradle',
          setting: 'minSdkVersion',
          value: '21',
          explanation: '设置最低支持的 Android API 级别'
        }
      ],
      buildSteps: [
        '确保已安装所有必需的工具',
        '配置 local.properties 文件',
        '运行 ./gradlew assembleDebug 构建调试版本',
        '或运行 ./gradlew assembleRelease 构建发布版本'
      ],
      commonIssues: [],
      examples: [],
      resources: []
    });

    // iOS 平台信息
    this.platformKnowledge.set('ios_build', {
      platform: 'iOS',
      topic: 'build',
      requirements: [
        'macOS 10.15 或更高版本',
        'Xcode 12.0 或更高版本',
        'iOS 10.0 或更高版本的部署目标',
        'CMake 3.16 或更高版本'
      ],
      configuration: [
        {
          file: 'Info.plist',
          setting: 'CFBundleIdentifier',
          value: 'com.yourcompany.yourapp',
          explanation: '设置应用程序包标识符'
        },
        {
          file: 'project.pbxproj',
          setting: 'IPHONEOS_DEPLOYMENT_TARGET',
          value: '10.0',
          explanation: '设置 iOS 部署目标版本'
        }
      ],
      buildSteps: [
        '使用 CMake 生成 Xcode 项目',
        '在 Xcode 中打开生成的项目',
        '选择目标设备或模拟器',
        '点击 Build 按钮构建项目'
      ],
      commonIssues: [],
      examples: [],
      resources: []
    });

    // Windows 平台信息
    this.platformKnowledge.set('windows_build', {
      platform: 'Windows',
      topic: 'build',
      requirements: [
        'Windows 10 或更高版本',
        'Visual Studio 2019 或更高版本',
        'CMake 3.16 或更高版本',
        'Git for Windows'
      ],
      configuration: [
        {
          file: 'CMakeLists.txt',
          setting: 'CMAKE_GENERATOR',
          value: 'Visual Studio 16 2019',
          explanation: '设置 CMake 生成器'
        },
        {
          file: 'CMakeLists.txt',
          setting: 'CMAKE_GENERATOR_PLATFORM',
          value: 'x64',
          explanation: '设置目标平台架构'
        }
      ],
      buildSteps: [
        '打开 Developer Command Prompt',
        '运行 cmake -B build -S .',
        '运行 cmake --build build --config Release',
        '或在 Visual Studio 中打开生成的解决方案文件'
      ],
      commonIssues: [],
      examples: [],
      resources: []
    });

    // macOS 平台信息
    this.platformKnowledge.set('macos_build', {
      platform: 'macOS',
      topic: 'build',
      requirements: [
        'macOS 10.15 或更高版本',
        'Xcode 12.0 或更高版本',
        'CMake 3.16 或更高版本'
      ],
      configuration: [
        {
          file: 'Info.plist',
          setting: 'CFBundleIdentifier',
          value: 'com.yourcompany.yourapp',
          explanation: '设置应用程序包标识符'
        },
        {
          file: 'CMakeLists.txt',
          setting: 'CMAKE_OSX_DEPLOYMENT_TARGET',
          value: '10.15',
          explanation: '设置 macOS 部署目标版本'
        }
      ],
      buildSteps: [
        '使用 CMake 生成 Xcode 项目或 Makefile',
        '如果使用 Xcode：在 Xcode 中打开项目并构建',
        '如果使用 Makefile：运行 make 命令'
      ],
      commonIssues: [],
      examples: [],
      resources: []
    });

    // Linux 平台信息
    this.platformKnowledge.set('linux_build', {
      platform: 'Linux',
      topic: 'build',
      requirements: [
        'Ubuntu 18.04 或更高版本（或等效的 Linux 发行版）',
        'GCC 7.0 或更高版本',
        'CMake 3.16 或更高版本',
        '必要的开发库（OpenGL、ALSA 等）'
      ],
      configuration: [
        {
          file: 'CMakeLists.txt',
          setting: 'CMAKE_BUILD_TYPE',
          value: 'Release',
          explanation: '设置构建类型'
        }
      ],
      buildSteps: [
        '安装必要的依赖包',
        '运行 cmake -B build -S . -DCMAKE_BUILD_TYPE=Release',
        '运行 cmake --build build',
        '运行生成的可执行文件'
      ],
      commonIssues: [],
      examples: [],
      resources: []
    });

    console.log(`🎯 初始化了 ${this.platformKnowledge.size} 个平台信息`);
  }

  /**
   * 获取内置平台信息
   */
  private getBuiltinPlatformInfo(platform: string, topic: string): PlatformInfo | null {
    const key = `${platform.toLowerCase()}_${topic.toLowerCase()}`;
    
    // 尝试精确匹配
    let info = this.platformKnowledge.get(key);
    if (info) return info;

    // 尝试模糊匹配
    for (const [mapKey, mapInfo] of this.platformKnowledge.entries()) {
      if (mapKey.includes(platform.toLowerCase()) && mapKey.includes(topic.toLowerCase())) {
        return mapInfo;
      }
    }

    // 尝试只匹配平台
    for (const [mapKey, mapInfo] of this.platformKnowledge.entries()) {
      if (mapKey.includes(platform.toLowerCase())) {
        return mapInfo;
      }
    }

    return null;
  }

  /**
   * 搜索官方平台文档
   */
  private async searchOfficialPlatformDocs(platform: string, topic: string): Promise<PlatformInfo | null> {
    try {
      // 尝试获取平台特定的文档
      const docPaths = [
        `docs/platforms/${platform.toLowerCase()}.md`,
        `docs/${platform.toLowerCase()}.md`,
        `platforms/${platform.toLowerCase()}/README.md`,
        `docs/build-${platform.toLowerCase()}.md`
      ];

      for (const path of docPaths) {
        try {
          const fileUrl = `${this.GITHUB_RAW_BASE}/${this.AXMOL_REPO}/dev/${path}`;
          const response = await networkUtils.get(fileUrl, { timeout: 8000 });
          
          const info = this.parsePlatformDocument(response.data, platform, topic);
          if (info) {
            console.log(`✅ 从官方文档获取平台信息: ${path}`);
            return info;
          }

        } catch (error) {
          continue;
        }
      }

    } catch (error) {
      console.log('⚠️ 官方平台文档搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 解析平台文档
   */
  private parsePlatformDocument(content: string, platform: string, topic: string): PlatformInfo | null {
    try {
      const lines = content.split('\n');
      const requirements: string[] = [];
      const buildSteps: string[] = [];
      const configuration: ConfigChange[] = [];

      let currentSection = '';
      let inRequirements = false;
      let inBuildSteps = false;

      for (const line of lines) {
        const trimmedLine = line.trim();

        if (trimmedLine.startsWith('# ') || trimmedLine.startsWith('## ')) {
          currentSection = trimmedLine.replace(/^#+\s*/, '').toLowerCase();
          inRequirements = currentSection.includes('requirement') || currentSection.includes('prerequisite');
          inBuildSteps = currentSection.includes('build') || currentSection.includes('compile');
        }

        if (inRequirements && (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* '))) {
          requirements.push(trimmedLine.substring(2));
        }

        if (inBuildSteps && (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ') || /^\d+\./.test(trimmedLine))) {
          buildSteps.push(trimmedLine.replace(/^[-*\d.]\s*/, ''));
        }
      }

      if (requirements.length > 0 || buildSteps.length > 0) {
        return {
          platform,
          topic,
          requirements,
          configuration,
          buildSteps,
          commonIssues: [],
          examples: [],
          resources: []
        };
      }

    } catch (error) {
      console.log('⚠️ 平台文档解析失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 搜索平台配置文件
   */
  private async searchPlatformConfigs(platform: string, topic: string): Promise<PlatformInfo | null> {
    try {
      // 根据平台查找相关的配置文件
      const configPaths = this.getPlatformConfigPaths(platform);

      for (const path of configPaths) {
        try {
          const fileUrl = `${this.GITHUB_RAW_BASE}/${this.AXMOL_REPO}/dev/${path}`;
          const response = await networkUtils.get(fileUrl, { timeout: 8000 });
          
          const info = this.extractConfigInfo(response.data, platform, topic, path);
          if (info) {
            console.log(`✅ 从配置文件获取平台信息: ${path}`);
            return info;
          }

        } catch (error) {
          continue;
        }
      }

    } catch (error) {
      console.log('⚠️ 平台配置搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 获取平台配置文件路径
   */
  private getPlatformConfigPaths(platform: string): string[] {
    const paths: string[] = [];
    const platformLower = platform.toLowerCase();

    switch (platformLower) {
      case 'android':
        paths.push(
          'templates/android/app/build.gradle',
          'templates/android/gradle.properties',
          'templates/android/local.properties.template'
        );
        break;
      case 'ios':
        paths.push(
          'templates/ios/Info.plist',
          'templates/ios/project.pbxproj.template'
        );
        break;
      case 'windows':
        paths.push(
          'CMakeLists.txt',
          'cmake/Modules/AxmolBuildSet.cmake'
        );
        break;
      case 'macos':
        paths.push(
          'templates/mac/Info.plist',
          'CMakeLists.txt'
        );
        break;
      case 'linux':
        paths.push(
          'CMakeLists.txt',
          'cmake/Modules/AxmolBuildSet.cmake'
        );
        break;
    }

    return paths;
  }

  /**
   * 从配置文件提取信息
   */
  private extractConfigInfo(content: string, platform: string, topic: string, filePath: string): PlatformInfo | null {
    // 简化的配置文件解析
    const configuration: ConfigChange[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 查找配置项
      if (trimmedLine.includes('=') && !trimmedLine.startsWith('#') && !trimmedLine.startsWith('//')) {
        const [key, value] = trimmedLine.split('=', 2);
        if (key && value) {
          configuration.push({
            file: filePath.split('/').pop() || filePath,
            setting: key.trim(),
            value: value.trim(),
            explanation: `从 ${filePath} 提取的配置项`
          });
        }
      }
    }

    if (configuration.length > 0) {
      return {
        platform,
        topic,
        requirements: [],
        configuration,
        buildSteps: [],
        commonIssues: [],
        examples: [],
        resources: []
      };
    }

    return null;
  }

  /**
   * 生成基础平台信息
   */
  private generateBasicPlatformInfo(platform: string, topic: string): PlatformInfo {
    return {
      platform,
      topic,
      requirements: [
        `${platform} 开发环境`,
        '最新版本的 Axmol 引擎',
        '适当的编译工具链'
      ],
      configuration: [],
      buildSteps: [
        '配置开发环境',
        '下载 Axmol 源码',
        '按照官方文档进行构建',
        '测试构建结果'
      ],
      commonIssues: [],
      examples: [],
      resources: [
        {
          type: 'official_docs',
          title: `${platform} 平台官方文档`,
          url: `https://github.com/${this.AXMOL_REPO}/wiki`,
          content: '',
          relevanceScore: 8,
          matchedTerms: [platform.toLowerCase(), topic],
          source: 'official'
        }
      ]
    };
  }

  /**
   * 增强平台信息
   */
  private async enhancePlatformInfo(info: PlatformInfo): Promise<PlatformInfo> {
    // 添加常见问题（如果没有的话）
    if (info.commonIssues.length === 0) {
      info.commonIssues = this.generateCommonPlatformIssues(info.platform);
    }

    // 添加示例代码（如果没有的话）
    if (info.examples.length === 0) {
      info.examples = this.generatePlatformExamples(info.platform, info.topic);
    }

    return info;
  }

  /**
   * 生成常见平台问题
   */
  private generateCommonPlatformIssues(platform: string): BuildIssue[] {
    const issues: BuildIssue[] = [];

    switch (platform.toLowerCase()) {
      case 'android':
        issues.push({
          platform: 'Android',
          errorMessage: 'NDK not found',
          errorType: 'configuration',
          solutions: [
            {
              title: '配置 Android NDK',
              description: '正确配置 Android NDK 路径',
              steps: [
                '下载并安装 Android NDK',
                '在 local.properties 中设置 ndk.dir',
                '重新同步项目'
              ],
              codeChanges: [],
              configChanges: [],
              priority: 'high',
              verified: true
            }
          ],
          relatedIssues: []
        });
        break;
      case 'ios':
        issues.push({
          platform: 'iOS',
          errorMessage: 'Code signing error',
          errorType: 'configuration',
          solutions: [
            {
              title: '配置代码签名',
              description: '正确配置 iOS 代码签名',
              steps: [
                '在 Xcode 中选择正确的开发团队',
                '配置 Bundle Identifier',
                '确保证书和描述文件有效'
              ],
              codeChanges: [],
              configChanges: [],
              priority: 'high',
              verified: true
            }
          ],
          relatedIssues: []
        });
        break;
    }

    return issues;
  }

  /**
   * 生成平台示例
   */
  private generatePlatformExamples(platform: string, topic: string): CodeExample[] {
    const examples: CodeExample[] = [];

    if (topic.toLowerCase().includes('build')) {
      examples.push({
        title: `${platform} 构建脚本示例`,
        language: 'cpp',
        code: `// ${platform} 平台特定的构建配置示例
#ifdef AX_PLATFORM_${platform.toUpperCase()}
    // ${platform} 特定的初始化代码
    // ...
#endif`,
        description: `${platform} 平台的构建配置示例`
      });
    }

    return examples;
  }
}

// 导出默认平台服务实例
export const platformService = new PlatformService();
