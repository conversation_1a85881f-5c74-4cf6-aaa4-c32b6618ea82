/**
 * 缓存工具类
 * 提供内存缓存和可选的磁盘持久化功能
 */

import fs from 'fs-extra';
import * as path from 'path';
import { CacheEntry, CacheOptions } from '../types/index.js';

export class CacheUtils<T = any> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private options: Required<CacheOptions>;
  private cacheDir: string;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(options?: CacheOptions) {
    this.options = {
      ttl: 30 * 60 * 1000, // 默认30分钟
      maxSize: 1000, // 默认最大1000条
      enablePersistence: false,
      ...options
    };

    this.cacheDir = path.join(process.cwd(), 'cache');
    
    if (this.options.enablePersistence) {
      this.initPersistence();
    }

    // 启动定期清理
    this.startCleanup();
  }

  /**
   * 设置缓存项
   */
  async set(key: string, data: T, ttl?: number): Promise<void> {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.options.ttl,
      key
    };

    // 检查缓存大小限制
    if (this.cache.size >= this.options.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, entry);

    // 持久化到磁盘
    if (this.options.enablePersistence) {
      await this.persistEntry(key, entry);
    }

    console.log(`💾 缓存已设置: ${key} (TTL: ${entry.ttl}ms)`);
  }

  /**
   * 获取缓存项
   */
  async get(key: string): Promise<T | null> {
    let entry = this.cache.get(key);

    // 如果内存中没有，尝试从磁盘加载
    if (!entry && this.options.enablePersistence) {
      entry = await this.loadEntry(key);
      if (entry) {
        this.cache.set(key, entry);
      }
    }

    if (!entry) {
      console.log(`❌ 缓存未命中: ${key}`);
      return null;
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      console.log(`⏰ 缓存已过期: ${key}`);
      await this.delete(key);
      return null;
    }

    console.log(`✅ 缓存命中: ${key}`);
    return entry.data;
  }

  /**
   * 删除缓存项
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);

    if (this.options.enablePersistence) {
      await this.deletePersistentEntry(key);
    }

    if (deleted) {
      console.log(`🗑️ 缓存已删除: ${key}`);
    }

    return deleted;
  }

  /**
   * 检查缓存项是否存在且未过期
   */
  async has(key: string): Promise<boolean> {
    const data = await this.get(key);
    return data !== null;
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    this.cache.clear();

    if (this.options.enablePersistence) {
      await this.clearPersistentCache();
    }

    console.log('🧹 缓存已清空');
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestEntry: string | null;
    newestEntry: string | null;
  } {
    const entries = Array.from(this.cache.entries());
    const now = Date.now();

    let oldest: [string, CacheEntry<T>] | null = null;
    let newest: [string, CacheEntry<T>] | null = null;

    entries.forEach(([key, entry]) => {
      if (!oldest || entry.timestamp < oldest[1].timestamp) {
        oldest = [key, entry];
      }
      if (!newest || entry.timestamp > newest[1].timestamp) {
        newest = [key, entry];
      }
    });

    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate: 0, // 需要实现命中率统计
      oldestEntry: oldest ? oldest[0] : null,
      newestEntry: newest ? newest[0] : null
    };
  }

  /**
   * 获取所有缓存键
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取未过期的缓存键
   */
  getValidKeys(): string[] {
    const now = Date.now();
    return Array.from(this.cache.entries())
      .filter(([_, entry]) => !this.isExpired(entry))
      .map(([key, _]) => key);
  }

  /**
   * 清理过期缓存
   */
  cleanup(): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期缓存项`);
    }

    return cleanedCount;
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * 驱逐最旧的缓存项
   */
  private evictOldest(): void {
    let oldest: [string, CacheEntry<T>] | null = null;

    for (const [key, entry] of this.cache.entries()) {
      if (!oldest || entry.timestamp < oldest[1].timestamp) {
        oldest = [key, entry];
      }
    }

    if (oldest) {
      this.cache.delete(oldest[0]);
      console.log(`🗑️ 驱逐最旧缓存项: ${oldest[0]}`);
    }
  }

  /**
   * 初始化持久化
   */
  private async initPersistence(): Promise<void> {
    try {
      await fs.ensureDir(this.cacheDir);
      console.log(`📁 缓存目录已创建: ${this.cacheDir}`);
    } catch (error) {
      console.error('❌ 初始化缓存目录失败:', error);
      this.options.enablePersistence = false;
    }
  }

  /**
   * 持久化缓存项
   */
  private async persistEntry(key: string, entry: CacheEntry<T>): Promise<void> {
    try {
      const filename = this.getFilename(key);
      const filepath = path.join(this.cacheDir, filename);
      await fs.writeJson(filepath, entry);
    } catch (error) {
      console.error(`❌ 持久化缓存项失败 ${key}:`, error);
    }
  }

  /**
   * 加载持久化的缓存项
   */
  private async loadEntry(key: string): Promise<CacheEntry<T> | undefined> {
    try {
      const filename = this.getFilename(key);
      const filepath = path.join(this.cacheDir, filename);

      if (await fs.pathExists(filepath)) {
        const entry = await fs.readJson(filepath) as CacheEntry<T>;
        return entry;
      }
    } catch (error) {
      console.error(`❌ 加载缓存项失败 ${key}:`, error);
    }

    return undefined;
  }

  /**
   * 删除持久化的缓存项
   */
  private async deletePersistentEntry(key: string): Promise<void> {
    try {
      const filename = this.getFilename(key);
      const filepath = path.join(this.cacheDir, filename);
      await fs.remove(filepath);
    } catch (error) {
      console.error(`❌ 删除持久化缓存项失败 ${key}:`, error);
    }
  }

  /**
   * 清空持久化缓存
   */
  private async clearPersistentCache(): Promise<void> {
    try {
      await fs.emptyDir(this.cacheDir);
    } catch (error) {
      console.error('❌ 清空持久化缓存失败:', error);
    }
  }

  /**
   * 获取缓存文件名
   */
  private getFilename(key: string): string {
    // 将键转换为安全的文件名
    return key.replace(/[^a-zA-Z0-9]/g, '_') + '.json';
  }

  /**
   * 启动定期清理
   */
  private startCleanup(): void {
    // 每5分钟清理一次过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * 停止定期清理
   */
  stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 销毁缓存实例
   */
  async destroy(): Promise<void> {
    this.stopCleanup();
    await this.clear();
  }
}

// 导出默认缓存实例
export const defaultCache = new CacheUtils({
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 500,
  enablePersistence: true
});
