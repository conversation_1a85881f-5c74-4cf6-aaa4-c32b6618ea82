#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Axmol MCP服务器监控脚本
 * 提供自动重启、日志记录、健康检查等功能
 */

let serverProcess = null;
let restartCount = 0;
const maxRestarts = 5;
const restartDelay = 5000; // 5秒

// 日志文件路径
const logDir = path.join(process.cwd(), 'logs');
const logFile = path.join(logDir, `mcp-server-${new Date().toISOString().split('T')[0]}.log`);

// 确保日志目录存在
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * 写入日志
 */
function writeLog(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}\n`;
  
  // 输出到控制台
  console.log(logMessage.trim());
  
  // 写入日志文件
  fs.appendFileSync(logFile, logMessage);
}

/**
 * 启动MCP服务器
 */
function startMCPServer() {
  writeLog('🚀 启动Axmol MCP服务器...', 'INFO');
  
  // 检查dist目录是否存在
  const distPath = path.join(process.cwd(), 'dist', 'index.js');
  if (!fs.existsSync(distPath)) {
    writeLog('❌ 找不到编译后的文件，请先运行 npm run build', 'ERROR');
    process.exit(1);
  }
  
  serverProcess = spawn('node', ['dist/index.js'], {
    cwd: process.cwd(),
    stdio: ['inherit', 'pipe', 'pipe'],
    env: { 
      ...process.env, 
      NODE_ENV: 'production',
      MCP_MONITOR: 'true'
    }
  });

  // 处理服务器输出
  serverProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      writeLog(`[SERVER] ${output}`, 'INFO');
    }
  });

  serverProcess.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      writeLog(`[SERVER] ${output}`, 'ERROR');
    }
  });

  // 处理服务器退出
  serverProcess.on('close', (code) => {
    writeLog(`⚠️ MCP服务器退出，退出码: ${code}`, 'WARN');
    
    if (code !== 0 && restartCount < maxRestarts) {
      restartCount++;
      writeLog(`🔄 ${restartDelay/1000}秒后重启... (第${restartCount}/${maxRestarts}次)`, 'INFO');
      setTimeout(startMCPServer, restartDelay);
    } else if (restartCount >= maxRestarts) {
      writeLog(`❌ 达到最大重启次数(${maxRestarts})，停止监控`, 'ERROR');
      process.exit(1);
    } else {
      writeLog('✅ 服务器正常退出', 'INFO');
      process.exit(0);
    }
  });

  serverProcess.on('error', (error) => {
    writeLog(`❌ 启动失败: ${error.message}`, 'ERROR');
    if (restartCount < maxRestarts) {
      restartCount++;
      writeLog(`🔄 ${restartDelay/1000}秒后重启... (第${restartCount}/${maxRestarts}次)`, 'INFO');
      setTimeout(startMCPServer, restartDelay);
    }
  });

  // 重置重启计数器（服务器成功运行30秒后）
  setTimeout(() => {
    if (serverProcess && !serverProcess.killed) {
      restartCount = 0;
      writeLog('✅ 服务器稳定运行，重置重启计数器', 'INFO');
    }
  }, 30000);

  return serverProcess;
}

/**
 * 健康检查
 */
function healthCheck() {
  if (serverProcess && !serverProcess.killed) {
    writeLog('💓 健康检查: 服务器运行正常', 'INFO');
  } else {
    writeLog('⚠️ 健康检查: 服务器未运行', 'WARN');
  }
}

/**
 * 优雅关闭
 */
function gracefulShutdown(signal) {
  writeLog(`🔄 收到${signal}信号，正在优雅关闭...`, 'INFO');
  
  if (serverProcess && !serverProcess.killed) {
    writeLog('🛑 正在关闭MCP服务器...', 'INFO');
    serverProcess.kill('SIGTERM');
    
    // 如果10秒后还没关闭，强制杀死
    setTimeout(() => {
      if (serverProcess && !serverProcess.killed) {
        writeLog('⚡ 强制关闭MCP服务器', 'WARN');
        serverProcess.kill('SIGKILL');
      }
    }, 10000);
  }
  
  setTimeout(() => {
    writeLog('👋 监控脚本退出', 'INFO');
    process.exit(0);
  }, 1000);
}

// 注册信号处理器
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 启动信息
writeLog('🎮 Axmol MCP服务器监控器启动', 'INFO');
writeLog(`📁 工作目录: ${process.cwd()}`, 'INFO');
writeLog(`📝 日志文件: ${logFile}`, 'INFO');
writeLog(`🔧 Node.js版本: ${process.version}`, 'INFO');

// 启动服务器
startMCPServer();

// 定期健康检查（每60秒）
setInterval(healthCheck, 60000);

// 显示使用说明
console.log('\n📖 使用说明:');
console.log('  Ctrl+C 或 SIGTERM: 优雅关闭');
console.log('  日志文件位置:', logFile);
console.log('  最大重启次数:', maxRestarts);
console.log('  重启延迟:', restartDelay/1000, '秒\n');
