# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime generated directories
logs/
cache/
temp/
tmp/

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test coverage
coverage/
*.lcov

# Runtime logs
*.log

# Lock files (keep package-lock.json for consistency)
# yarn.lock

# Temporary files
*.tmp
*.temp

# MCP specific
mcp-config-local.json
