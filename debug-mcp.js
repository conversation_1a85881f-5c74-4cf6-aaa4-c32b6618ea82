#!/usr/bin/env node

/**
 * Axmol MCP 服务器调试脚本
 * 用于诊断MCP服务器在Cursor中的加载问题
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Axmol MCP 服务器调试工具');
console.log('================================');

// 检查基本环境
console.log('\n📊 环境检查:');
console.log(`Node.js 版本: ${process.version}`);
console.log(`工作目录: ${process.cwd()}`);
console.log(`平台: ${process.platform}`);

// 检查文件存在性
const filesToCheck = [
    'dist/index.js',
    'package.json',
    'node_modules/@modelcontextprotocol/sdk/package.json'
];

console.log('\n📁 文件检查:');
filesToCheck.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`${exists ? '✅' : '❌'} ${file}`);
    if (exists && file === 'dist/index.js') {
        const stats = fs.statSync(file);
        console.log(`   大小: ${Math.round(stats.size / 1024)}KB`);
        console.log(`   修改时间: ${stats.mtime.toLocaleString()}`);
    }
});

// 检查package.json配置
console.log('\n📦 Package.json 检查:');
try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log(`✅ 包名: ${pkg.name}`);
    console.log(`✅ 版本: ${pkg.version}`);
    console.log(`✅ 主文件: ${pkg.main}`);
    console.log(`✅ 类型: ${pkg.type}`);
    
    // 检查关键依赖
    const mcpSdk = pkg.dependencies['@modelcontextprotocol/sdk'];
    console.log(`✅ MCP SDK: ${mcpSdk}`);
} catch (error) {
    console.log(`❌ 读取package.json失败: ${error.message}`);
}

// 测试MCP服务器启动
console.log('\n🚀 MCP服务器启动测试:');
console.log('正在启动服务器...');

const serverProcess = spawn('node', ['dist/index.js'], {
    cwd: process.cwd(),
    stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let errorOutput = '';

serverProcess.stdout.on('data', (data) => {
    output += data.toString();
});

serverProcess.stderr.on('data', (data) => {
    errorOutput += data.toString();
});

// 5秒后终止进程
setTimeout(() => {
    serverProcess.kill('SIGTERM');
    
    console.log('\n📋 服务器输出:');
    if (output) {
        console.log('✅ 标准输出:');
        console.log(output);
    }
    
    if (errorOutput) {
        console.log('❌ 错误输出:');
        console.log(errorOutput);
    }
    
    // 生成Cursor配置建议
    console.log('\n🎯 Cursor MCP 配置建议:');
    console.log('将以下配置添加到Cursor的MCP设置中:');
    console.log('```json');
    console.log(JSON.stringify({
        "mcpServers": {
            "axmol-mcp-server": {
                "command": "node",
                "args": [path.resolve('dist/index.js')],
                "cwd": process.cwd(),
                "env": {
                    "NODE_ENV": "production"
                }
            }
        }
    }, null, 2));
    console.log('```');
    
    console.log('\n🔧 故障排除步骤:');
    console.log('1. 确保Node.js版本 >= 18.0.0');
    console.log('2. 运行 npm install 确保依赖完整');
    console.log('3. 运行 npm run build 重新编译');
    console.log('4. 检查Cursor的MCP配置路径是否正确');
    console.log('5. 重启Cursor IDE');
    
    process.exit(0);
}, 5000);

serverProcess.on('error', (error) => {
    console.log(`❌ 启动失败: ${error.message}`);
    process.exit(1);
});
