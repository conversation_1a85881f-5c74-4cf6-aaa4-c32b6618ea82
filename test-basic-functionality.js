#!/usr/bin/env node

/**
 * 基本功能测试
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 测试 Axmol MCP 工具集基本功能...\n');

// 检查构建文件
function checkBuildFiles() {
  console.log('📋 检查构建文件:');
  
  const requiredFiles = [
    'dist/index.js',
    'dist/index.d.ts',
    'package.json'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`   ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allFilesExist = false;
  });
  
  return allFilesExist;
}

// 测试服务器启动
function testServerStartup() {
  return new Promise((resolve) => {
    console.log('\n📋 测试服务器启动:');
    
    const serverPath = path.join(__dirname, 'dist', 'index.js');
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname
    });

    let output = '';
    let hasStarted = false;
    
    server.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      // 检查启动成功的标志
      if (text.includes('Axmol MCP 服务器已启动')) {
        hasStarted = true;
        console.log('   ✅ 服务器启动成功');
        
        // 检查工具数量
        if (text.includes('可用工具数量: 10')) {
          console.log('   ✅ 10个工具已注册');
        }
        
        // 立即终止服务器
        setTimeout(() => {
          server.kill();
        }, 1000);
      }
    });

    server.stderr.on('data', (data) => {
      console.log('   ⚠️ 错误输出:', data.toString());
    });

    // 设置超时
    const timeout = setTimeout(() => {
      server.kill();
      if (!hasStarted) {
        console.log('   ❌ 服务器启动超时');
      }
      resolve({ success: hasStarted, output });
    }, 8000);

    server.on('close', (code) => {
      clearTimeout(timeout);
      if (hasStarted) {
        console.log('   ✅ 服务器正常关闭');
      }
      resolve({ success: hasStarted, output, code });
    });
  });
}

// 检查工具定义
function checkToolDefinitions() {
  console.log('\n📋 检查工具定义:');
  
  try {
    const indexPath = path.join(__dirname, 'dist', 'index.js');
    const content = fs.readFileSync(indexPath, 'utf8');
    
    const expectedTools = [
      'search_axmol_documentation',
      'find_code_examples', 
      'get_api_reference',
      'solve_build_issue',
      'get_migration_guide',
      'find_platform_specific_info',
      'analyze_axmol_code',
      'compare_axmol_versions',
      'get_best_practices',
      'search_community_solutions'
    ];

    let toolsFound = 0;
    
    expectedTools.forEach(tool => {
      const found = content.includes(`"${tool}"`);
      console.log(`   ${found ? '✅' : '❌'} ${tool}`);
      if (found) toolsFound++;
    });

    console.log(`\n📊 工具定义统计: ${toolsFound}/${expectedTools.length} 个工具已定义`);
    return toolsFound === expectedTools.length;
    
  } catch (error) {
    console.log('   ❌ 读取文件失败:', error.message);
    return false;
  }
}

async function main() {
  try {
    // 1. 检查构建文件
    const buildFilesOk = checkBuildFiles();
    
    // 2. 检查工具定义
    const toolDefinitionsOk = checkToolDefinitions();
    
    // 3. 测试服务器启动
    const serverResult = await testServerStartup();
    
    // 总结
    console.log('\n🎯 测试总结:');
    console.log(`   构建文件: ${buildFilesOk ? '✅' : '❌'}`);
    console.log(`   工具定义: ${toolDefinitionsOk ? '✅' : '❌'}`);
    console.log(`   服务器启动: ${serverResult.success ? '✅' : '❌'}`);
    
    if (buildFilesOk && toolDefinitionsOk && serverResult.success) {
      console.log('\n🎉 所有基本功能测试通过！Axmol MCP 工具集已准备就绪。');
    } else {
      console.log('\n⚠️ 部分测试未通过，请检查相关问题。');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

main();
